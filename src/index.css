/* Tailwind directives must come first */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom utilities for text truncation */
@layer utilities {
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-5 {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Smooth text overflow for long content */
  .text-overflow-fade {
    position: relative;
  }

  .text-overflow-fade::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 30px;
    height: 1.2em;
    background: linear-gradient(to right, transparent, var(--nextui-colors-background));
    pointer-events: none;
  }

  /* Force text truncation for notes */
  .notes-truncate {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
    display: block !important;
  }
}

/* Import external fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Import performance optimizations */
@import './styles/performance-optimizations.css';

/* Import modern loader styles */
@import './styles/loader.css';

:root {
  font-family: 'Inter', sans-serif;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Super sleek custom scrollbar */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* Dark mode scrollbar */
@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.2);
  }
}

/* Ultra-thin scrollbar for specific containers */
.scrollbar-ultra-thin::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

.scrollbar-ultra-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-ultra-thin::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 1px;
}

.scrollbar-ultra-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.15);
}

@media (prefers-color-scheme: dark) {
  .scrollbar-ultra-thin::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.05);
  }

  .scrollbar-ultra-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.15);
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Table improvements */
.heroui-table-cell {
  padding: 10px 12px !important;
}

/* Input focus styles */
.heroui-input:focus-within {
  box-shadow: 0 0 0 2px hsl(var(--heroui-primary-200)) !important;
}

/* Card hover effect */
.heroui-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.heroui-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}