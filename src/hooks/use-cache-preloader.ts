import { useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { CachePreloader } from '../services/cachePreloader';
import { useAuth } from '../context/AuthContext';

interface UserBehavior {
  visitedPages: string[];
  pageVisitCounts: Record<string, number>;
  sessionStartTime: number;
  lastActiveTime: number;
  totalSessions: number;
}

interface CachePreloaderConfig {
  strategy: 'aggressive' | 'balanced' | 'minimal' | 'smart';
  enableSmartPreloading: boolean;
  enableRouteBasedPreloading: boolean;
  preloadDelay: number;
}

// CRITICAL FIX: Reduce preloading aggressiveness to speed up login
const DEFAULT_CONFIG: CachePreloaderConfig = {
  strategy: 'minimal', // Changed from 'balanced' to 'minimal'
  enableSmartPreloading: false, // Disabled during login
  enableRouteBasedPreloading: true,
  preloadDelay: 3000 // Increased delay to 3 seconds
};

/**
 * Hook for intelligent cache preloading based on user behavior
 */
export const useCachePreloader = (config: Partial<CachePreloaderConfig> = {}) => {
  const { user } = useAuth();
  const location = useLocation();
  const [isInitialized, setIsInitialized] = useState(false);
  const [preloadingStatus, setPreloadingStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const behaviorRef = useRef<UserBehavior | null>(null);
  const configRef = useRef<CachePreloaderConfig>({ ...DEFAULT_CONFIG, ...config });

  // Initialize user behavior tracking
  useEffect(() => {
    if (!user) return;

    const initializeBehavior = () => {
      const stored = localStorage.getItem(`cache_behavior_${user.id}`);
      if (stored) {
        try {
          behaviorRef.current = JSON.parse(stored);
        } catch (error) {
          console.warn('Failed to parse stored behavior data:', error);
        }
      }

      if (!behaviorRef.current) {
        behaviorRef.current = {
          visitedPages: [],
          pageVisitCounts: {},
          sessionStartTime: Date.now(),
          lastActiveTime: Date.now(),
          totalSessions: 1
        };
      } else {
        // Update session info
        behaviorRef.current.sessionStartTime = Date.now();
        behaviorRef.current.totalSessions += 1;
      }

      saveBehavior();
    };

    initializeBehavior();
  }, [user]);

  // Track page visits
  useEffect(() => {
    if (!user || !behaviorRef.current) return;

    const currentPath = location.pathname;
    const behavior = behaviorRef.current;

    // Update visited pages
    if (!behavior.visitedPages.includes(currentPath)) {
      behavior.visitedPages.push(currentPath);
    }

    // Update visit counts
    behavior.pageVisitCounts[currentPath] = (behavior.pageVisitCounts[currentPath] || 0) + 1;
    behavior.lastActiveTime = Date.now();

    saveBehavior();
  }, [location.pathname, user]);

  // Save behavior to localStorage
  const saveBehavior = () => {
    if (!user || !behaviorRef.current) return;

    try {
      localStorage.setItem(`cache_behavior_${user.id}`, JSON.stringify(behaviorRef.current));
    } catch (error) {
      console.warn('Failed to save behavior data:', error);
    }
  };

  // Initialize cache preloading
  useEffect(() => {
    if (!user || isInitialized) return;

    const initializePreloading = async () => {
      setPreloadingStatus('loading');
      
      try {
        const { strategy, preloadDelay } = configRef.current;
        
        // Delay preloading to not interfere with initial page load
        await new Promise(resolve => setTimeout(resolve, preloadDelay));
        
        await CachePreloader.initialize(strategy);
        
        setIsInitialized(true);
        setPreloadingStatus('success');
      } catch (error) {
        setPreloadingStatus('error');
      }
    };

    initializePreloading();
  }, [user, isInitialized]);

  // Smart preloading based on user behavior
  useEffect(() => {
    if (!user || !isInitialized || !behaviorRef.current || !configRef.current.enableSmartPreloading) {
      return;
    }

    const performSmartPreloading = async () => {
      const behavior = behaviorRef.current!;
      const { preloadDelay } = configRef.current;

      // Delay smart preloading
      await new Promise(resolve => setTimeout(resolve, preloadDelay * 2));

      try {
        // Determine most visited pages
        const sortedPages = Object.entries(behavior.pageVisitCounts)
          .sort(([, a], [, b]) => b - a)
          .slice(0, 3)
          .map(([page]) => page);

        const userBehaviorData = {
          frequentPages: sortedPages,
          lastVisitedPage: location.pathname,
          sessionCount: behavior.totalSessions
        };

        await CachePreloader.smartPreload(userBehaviorData);
      } catch (error) {
        // Silently handle smart preloading errors
      }
    };

    performSmartPreloading();
  }, [user, isInitialized, location.pathname]);

  // Route-based cache warming
  useEffect(() => {
    if (!user || !isInitialized || !configRef.current.enableRouteBasedPreloading) {
      return;
    }

    const warmCacheForRoute = async () => {
      const currentPath = location.pathname;
      const { preloadDelay } = configRef.current;

      // Route-specific cache warming mapping
      const routeCacheMap: Record<string, string[]> = {
        '/': ['trades'],
        '/analytics': ['analytics', 'performance', 'risk', 'metrics', 'drawdown', 'trades'],
        '/deep-analytics': ['analytics', 'performance', 'risk', 'metrics', 'drawdown', 'trades'],
        '/tax-analytics': ['tax', 'trades'],
        '/monthly-performance': ['monthly-performance', 'trades'],
        '/chart-viewer': ['charts']
      };

      const cacheTypes = routeCacheMap[currentPath];
      if (cacheTypes) {
        // Delay to not interfere with page rendering
        setTimeout(() => {
          CachePreloader.warmCache(cacheTypes);
        }, preloadDelay / 2);
      }
    };

    warmCacheForRoute();
  }, [location.pathname, user, isInitialized]);

  // Predictive preloading based on navigation patterns
  const predictivePreload = async (targetRoute: string) => {
    if (!isInitialized) return;

    const routeCacheMap: Record<string, string[]> = {
      '/analytics': ['analytics', 'performance', 'risk', 'drawdown'],
      '/deep-analytics': ['analytics', 'performance', 'risk', 'drawdown'],
      '/tax-analytics': ['tax'],
      '/monthly-performance': ['monthly-performance'],
      '/chart-viewer': ['charts']
    };

    const cacheTypes = routeCacheMap[targetRoute];
    if (cacheTypes) {
      await CachePreloader.warmCache(cacheTypes);
    }
  };

  // Get user behavior insights
  const getBehaviorInsights = () => {
    if (!behaviorRef.current) return null;

    const behavior = behaviorRef.current;
    const mostVisitedPage = Object.entries(behavior.pageVisitCounts)
      .sort(([, a], [, b]) => b - a)[0];

    return {
      totalPages: behavior.visitedPages.length,
      mostVisitedPage: mostVisitedPage ? mostVisitedPage[0] : null,
      mostVisitedCount: mostVisitedPage ? mostVisitedPage[1] : 0,
      sessionDuration: Date.now() - behavior.sessionStartTime,
      totalSessions: behavior.totalSessions,
      isFrequentUser: behavior.totalSessions > 5,
      isAnalyticsUser: behavior.visitedPages.some(page => page.includes('analytics'))
    };
  };

  // Manual cache operations
  const clearCache = () => {
    CachePreloader.reset();
    setIsInitialized(false);
    setPreloadingStatus('idle');
  };

  const warmCache = (types: string[]) => {
    if (isInitialized) {
      return CachePreloader.warmCache(types);
    }
  };

  return {
    isInitialized,
    preloadingStatus,
    behaviorInsights: getBehaviorInsights(),
    predictivePreload,
    clearCache,
    warmCache,
    stats: isInitialized ? CachePreloader.getStats() : null
  };
};

/**
 * Hook for monitoring cache performance
 */
export const useCachePerformance = () => {
  const [metrics, setMetrics] = useState({
    hitRate: 0,
    missRate: 0,
    avgLoadTime: 0,
    totalRequests: 0
  });

  useEffect(() => {
    // This would integrate with actual performance monitoring
    // For now, we'll use mock data
    const updateMetrics = () => {
      const stats = CachePreloader.getStats();
      if (stats) {
        setMetrics({
          hitRate: stats.successfulPreloads / Math.max(stats.totalPreloads, 1) * 100,
          missRate: stats.failedPreloads / Math.max(stats.totalPreloads, 1) * 100,
          avgLoadTime: stats.totalTimeMs / Math.max(stats.totalPreloads, 1),
          totalRequests: stats.totalPreloads
        });
      }
    };

    const interval = setInterval(updateMetrics, 5000);
    updateMetrics();

    return () => clearInterval(interval);
  }, []);

  return metrics;
};
