import React from "react";
import { Trade } from "../types/trade";
import { mockTrades } from "../data/mock-trades";
import { useTruePortfolioWithTrades } from "./use-true-portfolio-with-trades";
import { useGlobalFilter } from "../context/GlobalFilterContext";
import { isInGlobalFilter } from "../utils/dateFilterUtils";
import { useAccountingMethod } from "../context/AccountingMethodContext";
import { getTradeDateForAccounting, getExitDatesWithFallback } from "../utils/accountingUtils";
import { v4 as uuidv4 } from 'uuid';

// CRITICAL FIX: Month name normalizer to handle "Sept" vs "Sep" inconsistency
const normalizeMonthName = (monthName: string): string => {
  const monthMap: { [key: string]: string } = {
    'Jan': 'Jan', 'Feb': 'Feb', 'Mar': 'Mar', 'Apr': 'Apr',
    'May': 'May', 'Jun': 'Jun', 'Jul': 'Jul', 'Aug': 'Aug',
    'Sep': 'Sep', 'Sept': 'Sep', // CRITICAL: Handle Sept -> Sep
    'Oct': 'Oct', 'Nov': 'Nov', 'Dec': 'Dec'
  };
  return monthMap[monthName] || monthName;
};
import {
  calcAvgEntry,
  calcPositionSize,
  calcAllocation,
  calcSLPercent,
  calcOpenQty,
  calcExitedQty,
  calcAvgExitPrice,
  calcStockMove,
  calcRewardRisk,
  calcHoldingDays,
  calcRealisedAmount,
  calcPFImpact,
  calcRealizedPL_FIFO
} from "../utils/tradeCalculations";

/**
 * Utility function to get consistent chronological sorting comparator
 * This ensures %PF calculations are always consistent regardless of table sorting
 */
export const getChronologicalSortComparator = () => {
  return (a: Trade, b: Trade) => {
    // Sort by trade number first (primary sequence), then by date as fallback
    const tradeNoA = Number(a.tradeNo) || 0;
    const tradeNoB = Number(b.tradeNo) || 0;

    if (tradeNoA !== tradeNoB) {
      return tradeNoA - tradeNoB;
    }

    // Fallback to date if trade numbers are equal
    if (a.date && b.date) {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    }

    return 0;
  };
};
import { calculateTradePL } from "../utils/accountingUtils";
import { SupabaseService } from "../services/supabaseService";
// Migrated from IndexedDB to Supabase with authentication

// Define SortDirection type compatible with HeroUI Table
type SortDirection = "ascending" | "descending";

export interface SortDescriptor {
  column: string;
  direction: SortDirection;
}

// Removed local storage constants - app is now purely cloud-based

// Supabase helpers
async function getTradesFromSupabase(): Promise<Trade[]> {
  if (typeof window === 'undefined') return []; // In a server-side environment, return empty array

  try {
    const trades = await SupabaseService.getAllTrades();
    return trades;
  } catch (error) {
    return []; // Always return empty array on error to prevent mock data
  }
}

// Debounce mechanism to prevent multiple simultaneous saves
let saveTimeout: NodeJS.Timeout | null = null;
let isSaving = false;

// Track which trades have been modified for incremental saves
let modifiedTrades = new Set<string>();
let deletedTrades = new Set<string>();
let isIncrementalSave = false;
let trackingEnabled = true;

async function saveTradesToSupabase(trades: Trade[]): Promise<boolean> {
  if (typeof window === 'undefined') return false;

  // OPTIMIZED: Minimal logging for production performance

  // Clear any existing timeout for regular debounced saves
  if (saveTimeout) {
    clearTimeout(saveTimeout);
  }

  // If already saving, skip this call
  if (isSaving) {
    return true;
  }

  return new Promise((resolve) => {
    // OPTIMIZED: 1-second debounced save with minimal logging
    saveTimeout = setTimeout(async () => {
      if (isSaving) {
        resolve(true);
        return;
      }

      isSaving = true;

      try {
        let success = false;
        const totalChanges = modifiedTrades.size + deletedTrades.size;

        if (totalChanges === 0) {
          success = true;
          isIncrementalSave = false;
        } else {
          // Ensure we have the latest trades data before saving
          const currentTrades = trades.filter(t => t.id && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t.id));
          success = await performIncrementalSave(currentTrades);
          isIncrementalSave = true;
        }

        // Clear tracking sets after successful save
        if (success) {
          modifiedTrades.clear();
          deletedTrades.clear();
          // Clear cache to ensure consistency after incremental save
          SupabaseService.clearTradesCache();
        }

        resolve(success);
      } catch (error) {
        console.error('❌ Save error:', error);
        resolve(false);
      } finally {
        isSaving = false;
      }
    }, 1000); // 1 second debounce
  });
}

// OPTIMIZED: Incremental save function with minimal logging
async function performIncrementalSave(trades: Trade[]): Promise<boolean> {
  try {
    let allSuccessful = true;

    console.log(`💾 Incremental save: ${modifiedTrades.size} modified, ${deletedTrades.size} deleted, ${trades.length} total trades`);

    // Handle deleted trades first
    for (const deletedId of deletedTrades) {
      const success = await SupabaseService.deleteTrade(deletedId);
      if (!success) {
        allSuccessful = false;
        console.error(`❌ Delete failed: ${deletedId}`);
      }
    }

    // Handle modified/new trades
    for (const tradeId of modifiedTrades) {
      const trade = trades.find(t => t.id === tradeId);
      if (trade) {
        const success = await SupabaseService.saveTrade(trade);
        if (!success) {
          allSuccessful = false;
          console.error(`❌ Save failed: ${trade.name || 'Unnamed'}`);
        }
      } else {
        // Trade not found - likely deleted or replaced, remove from tracking
        console.log(`🧹 Cleaning stale trade ID from tracking: ${tradeId}`);
        modifiedTrades.delete(tradeId);
        // Don't mark as failed since this is just cleanup
      }
    }

    return allSuccessful;
  } catch (error) {
    console.error('❌ Incremental save error:', error);
    return false;
  }
}

// Helper function to track trade modifications
function trackTradeModification(tradeId: string) {
  if (!trackingEnabled || !tradeId) return;
  // Only track valid UUIDs to prevent tracking temporary or invalid IDs
  if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(tradeId)) {
    modifiedTrades.add(tradeId);
  }
}

// Helper function to track trade deletions
function trackTradeDeletion(tradeId: string) {
  if (!trackingEnabled || !tradeId) return;
  // Only track valid UUIDs to prevent tracking temporary or invalid IDs
  if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(tradeId)) {
    deletedTrades.add(tradeId);
    modifiedTrades.delete(tradeId); // Remove from modified if it was there
  }
}

// Helper function to clear tracking (for bulk operations)
function clearTradeTracking() {
  modifiedTrades.clear();
  deletedTrades.clear();
  // Debug logging removed for production
}

// Helper function to disable tracking temporarily (for bulk operations)
function disableTracking() {
  trackingEnabled = false;
  clearTradeTracking();

}

// Helper function to re-enable tracking
function enableTracking() {
  trackingEnabled = true;
  console.log(`▶️ Re-enabled trade tracking`);
}

async function getTradeSettings() {
  if (typeof window === 'undefined') return null;
  try {
    const settings = await SupabaseService.getTradeSettings();
    return settings;
  } catch (error) {
    return null;
  }
}

async function saveTradeSettings(settings: any): Promise<boolean> {
  if (typeof window === 'undefined') return false;
  try {
    return await SupabaseService.saveTradeSettings(settings);
  } catch (error) {
    return false;
  }
}

async function clearAllTradeAndSettingsData(): Promise<boolean> {
  if (typeof window === 'undefined') return false;
  try {
    // CRITICAL SECURITY FIX: Require explicit confirmation for destructive operation
    const success = await SupabaseService.clearAllData(true); // Pass confirmDestruction=true

    // Local storage cleanup removed - app is now purely cloud-based

    return success;
  } catch (error) {
    return false;
  }
}

// Utility to recalculate all calculated fields for all trades
// This function is now a pure function and takes getTruePortfolioSize and accounting method as explicit arguments.
// Added skipExpensiveCalculations flag to optimize bulk imports
function recalculateAllTrades(
  trades: Trade[],
  getTruePortfolioSize: (month: string, year: number) => number,
  useCashBasis: boolean = false,
  skipExpensiveCalculations: boolean = false
): Trade[] {
  // Sort trades by date (or tradeNo as fallback) for cummPf calculation
  const sorted = [...trades].sort((a, b) => {
    if (a.date && b.date) {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    }
    return (a.tradeNo || '').localeCompare(b.tradeNo || '');
  });

  let runningCummPf = 0;

  // If skipping expensive calculations, return trades with minimal processing
  if (skipExpensiveCalculations) {
    return sorted.map(trade => ({
      ...trade,
      name: (trade.name || '').toUpperCase(),
      // Keep existing calculated values or set minimal defaults
      avgEntry: trade.avgEntry || trade.entry || 0,
      positionSize: trade.positionSize || 0,
      allocation: trade.allocation || 0,
      slPercent: trade.slPercent || 0,
      openQty: trade.openQty || trade.initialQty || 0,
      exitedQty: trade.exitedQty || 0,
      avgExitPrice: trade.avgExitPrice || 0,
      stockMove: trade.stockMove || 0,
      holdingDays: trade.holdingDays || 0,
      realisedAmount: trade.realisedAmount || 0,
      plRs: trade.plRs || 0,
      pfImpact: trade.pfImpact || 0,
      cummPf: trade.cummPf || 0,
      // Mark as needing recalculation
      _needsRecalculation: true
    }));
  }

  // First pass for individual trade calculations
  const calculatedTrades = sorted.map((trade) => {
    // Original entry and pyramid entries for calculations
    const allEntries = [
      { price: Number(trade.entry || 0), qty: Number(trade.initialQty || 0) },
      { price: Number(trade.pyramid1Price || 0), qty: Number(trade.pyramid1Qty || 0) },
      { price: Number(trade.pyramid2Price || 0), qty: Number(trade.pyramid2Qty || 0) }
    ].filter(e => e.qty > 0 && e.price > 0); // Filter out entries with 0 qty or price

    const avgEntry = calcAvgEntry(allEntries);
    const totalInitialQty = allEntries.reduce((sum, e) => sum + e.qty, 0);
    const positionSize = calcPositionSize(avgEntry, totalInitialQty);

    // Get the true portfolio size for the trade's entry date (for allocation calculation)
    let tradePortfolioSize = 100000; // Default fallback
    if (trade.date && getTruePortfolioSize) { // Use the passed getTruePortfolioSize
      const tradeDate = new Date(trade.date);
      const rawMonth = tradeDate.toLocaleString('default', { month: 'short' });
      const month = normalizeMonthName(rawMonth); // CRITICAL FIX: Normalize Sept -> Sep
      const year = tradeDate.getFullYear();
      try {
        tradePortfolioSize = getTruePortfolioSize(month, year) || 100000;
      } catch (error) {
        tradePortfolioSize = 100000; // Fallback
      }
    }

    // CRITICAL FIX: Preserve existing allocation, don't recalculate
    const allocation = trade.allocation || calcAllocation(positionSize, tradePortfolioSize);
    const slPercent = calcSLPercent(trade.sl, trade.entry);

    // Exit legs
    const allExits = [
      { price: Number(trade.exit1Price || 0), qty: Number(trade.exit1Qty || 0) },
      { price: Number(trade.exit2Price || 0), qty: Number(trade.exit2Qty || 0) },
      { price: Number(trade.exit3Price || 0), qty: Number(trade.exit3Qty || 0) }
    ].filter(e => e.qty > 0 && e.price > 0); // Filter out exits with 0 qty or price

    const exitedQty = allExits.reduce((sum, e) => sum + e.qty, 0);
    const openQty = totalInitialQty - exitedQty;
    const avgExitPrice = calcAvgExitPrice(allExits); // Avg price of actual exits

    const stockMove = calcStockMove(
      avgEntry,
      avgExitPrice,
      trade.cmp,
      openQty,
      exitedQty,
      trade.positionStatus,
      trade.buySell
    );

    const rewardRisk = calcRewardRisk(
      trade.cmp || avgExitPrice || trade.entry,
      trade.entry,
      trade.sl,
      trade.positionStatus,
      avgExitPrice,
      openQty,
      exitedQty,
      trade.buySell
    );

    const pyramidDates = [];
    if (trade.pyramid1Date && trade.pyramid1Qty) pyramidDates.push({ date: trade.pyramid1Date, qty: trade.pyramid1Qty });
    if (trade.pyramid2Date && trade.pyramid2Qty) pyramidDates.push({ date: trade.pyramid2Date, qty: trade.pyramid2Qty });

    const exitDatesForHolding = [];
    if (trade.exit1Date && trade.exit1Qty) exitDatesForHolding.push({ date: trade.exit1Date, qty: trade.exit1Qty });
    if (trade.exit2Date && trade.exit2Qty) exitDatesForHolding.push({ date: trade.exit2Date, qty: trade.exit2Qty });
    if (trade.exit3Date && trade.exit3Qty) exitDatesForHolding.push({ date: trade.exit3Date, qty: trade.exit3Qty });

    let primaryExitDateForHolding: string | null = null;
    if (allExits.length > 0) {
        const validExitDates = [trade.exit1Date, trade.exit2Date, trade.exit3Date].filter(Boolean) as string[];
        if (validExitDates.length > 0) {
            primaryExitDateForHolding = validExitDates.sort((a,b) => new Date(a).getTime() - new Date(b).getTime())[0];
        }
    }
    if (trade.positionStatus !== "Open" && !primaryExitDateForHolding && allExits.length > 0) {
        primaryExitDateForHolding = trade.date;
    }

    const holdingDays = calcHoldingDays(
        trade.date,
        primaryExitDateForHolding,
        pyramidDates,
        exitDatesForHolding
    );

    const realisedAmount = calcRealisedAmount(exitedQty, avgExitPrice);

    const entryLotsForFifo = allEntries.map(e => ({ price: e.price, qty: e.qty }));
    const exitLotsForFifo = allExits.map(e => ({ price: e.price, qty: e.qty }));

    const plRs = exitedQty > 0 ? calcRealizedPL_FIFO(entryLotsForFifo, exitLotsForFifo, trade.buySell as 'Buy' | 'Sell') : 0;

    // Calculate accounting-aware P/L and PF Impact using correct portfolio size
    const accountingAwarePL = calculateTradePL({...trade, plRs}, useCashBasis);
    const accountingAwarePortfolioSize = getTruePortfolioSize ?
      (() => {
        try {
          const relevantDate = getTradeDateForAccounting(trade, useCashBasis);
          const date = new Date(relevantDate);
          const rawMonth = date.toLocaleString('default', { month: 'short' });
          const month = normalizeMonthName(rawMonth); // CRITICAL FIX: Normalize Sept -> Sep
          const year = date.getFullYear();
          return getTruePortfolioSize(month, year) || 100000;
        } catch {
          return 100000;
        }
      })() : 100000;
    const pfImpact = calcPFImpact(accountingAwarePL, accountingAwarePortfolioSize);

    const finalOpenQty = Math.max(0, openQty);

    // Destructure to omit openHeat if it exists on the trade object
    const { openHeat, ...restOfTrade } = trade as any; // Use 'as any' for robust destructuring if openHeat might not exist

    // Calculate position status based on quantities ONLY if user has never manually set it
    let calculatedPositionStatus = restOfTrade.positionStatus; // Keep existing by default

    const hasUserEditedPositionStatus = restOfTrade._userEditedFields?.includes('positionStatus');
    if (!hasUserEditedPositionStatus) {
      // Auto-calculate position status only if user hasn't manually set it
      if (finalOpenQty <= 0 && exitedQty > 0) {
        calculatedPositionStatus = 'Closed';
      } else if (exitedQty > 0 && finalOpenQty > 0) {
        calculatedPositionStatus = 'Partial';
      } else {
        calculatedPositionStatus = 'Open';
      }

    }

    // Preserve user-controlled fields that should not be auto-updated
    const userControlledFields = ['positionStatus', 'buySell', 'setup', 'exitTrigger', 'proficiencyGrowthAreas', 'planFollowed', 'notes', 'tradeNo'];
    const preservedFields: Record<string, any> = {};

    userControlledFields.forEach(field => {
      if (restOfTrade._userEditedFields?.includes(field) && restOfTrade[field as keyof Trade] !== undefined) {
        preservedFields[field] = restOfTrade[field as keyof Trade];
      }
    });

    return {
      ...restOfTrade,
      // Apply calculated fields
      name: (restOfTrade.name || '').toUpperCase(),
      avgEntry,
      positionSize,
      allocation,
      slPercent,
      openQty: finalOpenQty,
      exitedQty,
      avgExitPrice,
      stockMove,
      holdingDays,
      realisedAmount,
      plRs,
      pfImpact,
      positionStatus: calculatedPositionStatus, // Use calculated or preserved status
      cummPf: 0, // Placeholder, will be updated in second pass
      // Preserve user-edited fields
      ...preservedFields,
      // Always preserve the user edit tracking
      _userEditedFields: restOfTrade._userEditedFields || []
    };
  });

  // Store accounting-aware values for later use, but don't calculate cumulative PF yet
  // Cumulative PF will be calculated after all processing (expansion, grouping, etc.) is done
  return calculatedTrades.map((trade) => {
    // Calculate both accrual and cash basis values for storage
    const accrualPL = trade.plRs || 0;
    const cashPL = calculateTradePL(trade, true); // Cash basis P/L

    // Helper function to get portfolio size based on accounting method
    const getPortfolioSizeForAccounting = (useCashBasisForCalc: boolean) => {
      if (!getTruePortfolioSize) return 100000;

      try {
        const relevantDate = getTradeDateForAccounting(trade, useCashBasisForCalc);
        const date = new Date(relevantDate);
        const rawMonth = date.toLocaleString('default', { month: 'short' });
        const month = normalizeMonthName(rawMonth); // CRITICAL FIX: Normalize Sept -> Sep
        const year = date.getFullYear();
        return getTruePortfolioSize(month, year) || 100000;
      } catch {
        return 100000;
      }
    };

    // Get portfolio sizes for both accounting methods
    const accrualPortfolioSize = getPortfolioSizeForAccounting(false); // Entry date portfolio
    const cashPortfolioSize = getPortfolioSizeForAccounting(true);     // Exit date portfolio

    // Calculate PF impact using correct portfolio size for each method
    const accrualPfImpact = trade.positionStatus !== 'Open' ?
      calcPFImpact(accrualPL, accrualPortfolioSize) : 0;
    const cashPfImpact = trade.positionStatus !== 'Open' ?
      calcPFImpact(cashPL, cashPortfolioSize) : 0;

    // Store both values to avoid recalculation at display time
    return {
      ...trade,
      // Store both accounting method values
      _accrualPL: accrualPL,
      _cashPL: cashPL,
      _accrualPfImpact: accrualPfImpact,
      _cashPfImpact: cashPfImpact,
      cummPf: 0, // Will be calculated later after all processing
    };
  });
}

// Define ALL_COLUMNS here, as it's closely tied to the hook's state
const ALL_COLUMNS = [
  'tradeNo', 'date', 'name', 'setup', 'buySell', 'entry', 'sl', 'slPercent', 'tsl', 'cmp',
  'initialQty', 'pyramid1Price', 'pyramid1Qty', 'pyramid1Date', 'pyramid2Price', 'pyramid2Qty', 'pyramid2Date',
  'positionSize', 'allocation', 'exit1Price', 'exit1Qty', 'exit1Date', 'exit2Price', 'exit2Qty', 'exit2Date',
  'exit3Price', 'exit3Qty', 'exit3Date', 'openQty', 'exitedQty', 'avgExitPrice', 'stockMove', 'openHeat',
  'rewardRisk', 'holdingDays', 'positionStatus', 'realisedAmount', 'plRs', 'pfImpact', 'cummPf',
  'planFollowed', 'exitTrigger', 'proficiencyGrowthAreas', 'unrealizedPL', 'actions', 'notes'
];

// All columns enabled by default as requested
const DEFAULT_VISIBLE_COLUMNS = [
  'tradeNo', 'date', 'name', 'setup', 'buySell', 'entry', 'avgEntry', 'sl', 'slPercent', 'tsl', 'cmp',
  'initialQty', 'pyramid1Price', 'pyramid1Qty', 'pyramid1Date', 'pyramid2Price', 'pyramid2Qty', 'pyramid2Date',
  'positionSize', 'allocation', 'exit1Price', 'exit1Qty', 'exit1Date', 'exit2Price', 'exit2Qty', 'exit2Date',
  'exit3Price', 'exit3Qty', 'exit3Date', 'openQty', 'exitedQty', 'avgExitPrice', 'stockMove', 'openHeat',
  'rewardRisk', 'holdingDays', 'positionStatus', 'realisedAmount', 'plRs', 'pfImpact', 'cummPf',
  'planFollowed', 'exitTrigger', 'proficiencyGrowthAreas', 'chartAttachments', 'actions', 'unrealizedPL', 'notes'
];

export const useTrades = () => {
  const [trades, setTrades] = React.useState<Trade[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const [isRecalculating, setIsRecalculating] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [statusFilter, setStatusFilter] = React.useState('');
  const [sortDescriptor, setSortDescriptor] = React.useState<SortDescriptor>({ column: 'tradeNo', direction: 'ascending' });
  const [visibleColumns, setVisibleColumns] = React.useState<string[]>(DEFAULT_VISIBLE_COLUMNS);
  const { filter: globalFilter } = useGlobalFilter();
  const { accountingMethod } = useAccountingMethod();
  const useCashBasis = accountingMethod === 'cash';

  // Track previous accounting method to avoid unnecessary recalculations
  const prevAccountingMethodRef = React.useRef<string>(accountingMethod);

  // Get true portfolio functions - use empty array to avoid circular dependency
  const { portfolioSize, getPortfolioSize } = useTruePortfolioWithTrades([]);

  // Memoize the recalculation helper that wraps the pure `recalculateAllTrades` function.
  // Use a stable reference to getPortfolioSize to prevent infinite loops
  // CRITICAL FIX: Freeze portfolio sizes during calculation to prevent infinite recalculations
  const portfolioSizeSnapshot = React.useRef(new Map<string, number>());
  const snapshotTimestamp = React.useRef(0);

  const stableGetPortfolioSize = React.useCallback((month: string, year: number) => {
    const key = `${month}-${year}`;

    // CRITICAL FIX: Always use snapshot during calculations to prevent instability
    if (isCalculatingRef.current && portfolioSizeSnapshot.current.has(key)) {
      const snapshotSize = portfolioSizeSnapshot.current.get(key)!;
      return snapshotSize;
    }

    // If not calculating, get fresh size but don't update snapshot during calculation
    const size = getPortfolioSize(month, year);

    // Only update snapshot if we're not in the middle of a calculation
    if (!isCalculatingRef.current) {
      portfolioSizeSnapshot.current.set(key, size);
    }

    return size;
  }, [getPortfolioSize]);

  const recalculateTradesWithCurrentPortfolio = React.useCallback((tradesToRecalculate: Trade[], skipExpensiveCalculations: boolean = false) => {
    return recalculateAllTrades(tradesToRecalculate, stableGetPortfolioSize, useCashBasis, skipExpensiveCalculations);
  }, [stableGetPortfolioSize, useCashBasis]);

  // CRITICAL FIX: Protected recalculation function that prevents race conditions
  const safeRecalculateTradesWithLock = React.useCallback((
    tradesToRecalculate: Trade[],
    skipExpensiveCalculations: boolean = false,
    reason: string = 'unknown'
  ) => {
    if (isCalculatingRef.current) {
      return tradesToRecalculate; // Return unchanged trades if calculation is in progress
    }

    isCalculatingRef.current = true;

    // CRITICAL FIX: Create portfolio size snapshot at start of calculation
    portfolioSizeSnapshot.current.clear();
    snapshotTimestamp.current = Date.now();

    try {
      const result = recalculateAllTrades(tradesToRecalculate, stableGetPortfolioSize, useCashBasis, skipExpensiveCalculations);
      return result;
    } catch (error) {
      return tradesToRecalculate; // Return unchanged trades on error
    } finally {
      isCalculatingRef.current = false;

      // Process any queued calculations
      if (calculationQueueRef.current.length > 0) {
        const nextCalculation = calculationQueueRef.current.shift();
        if (nextCalculation) {
          setTimeout(nextCalculation, 50); // Small delay to prevent immediate re-entry
        }
      }
    }
  }, [stableGetPortfolioSize, useCashBasis]);

  // Performance optimization: Cache expensive calculations
  const calculationCache = React.useRef(new Map<string, any>());
  const lastCalculationHash = React.useRef<string>('');

  // CRITICAL FIX: Add calculation lock to prevent race conditions
  const isCalculatingRef = React.useRef(false);
  const calculationQueueRef = React.useRef<(() => void)[]>([]);

  // PERFORMANCE OPTIMIZATION: Smart memoization with incremental updates
  const processedTrades = React.useMemo(() => {
    const startTime = performance.now();

    // Create a lightweight hash for change detection
    const currentHash = `${trades.length}-${searchQuery}-${statusFilter}-${sortDescriptor.column}-${sortDescriptor.direction}-${globalFilter}-${accountingMethod}`;

    // Return cached result if nothing changed
    if (currentHash === lastCalculationHash.current && calculationCache.current.has('processedTrades')) {
      const cached = calculationCache.current.get('processedTrades');
      return cached;
    }

    let filtered = trades;

    // Optimized search filter with early termination
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(trade => {
        // Check most common fields first for early termination
        return trade.name?.toLowerCase().includes(query) ||
               trade.tradeNo?.toLowerCase().includes(query) ||
               trade.setup?.toLowerCase().includes(query) ||
               trade.notes?.toLowerCase().includes(query);
      });
    }

    // Apply status filter (most selective first)
    if (statusFilter) {
      filtered = filtered.filter(trade => trade.positionStatus === statusFilter);
    }

    // Apply global date filter with optimized date checking
    if (globalFilter && (globalFilter as any) !== 'all') {
      filtered = filtered.filter(trade => isInGlobalFilter(trade as any, globalFilter as any));
    }

    // Optimized sorting with stable sort
    if (sortDescriptor.column) {
      const column = sortDescriptor.column as keyof Trade;
      const isDescending = sortDescriptor.direction === 'descending';

      filtered = [...filtered].sort((a, b) => {
        const aValue = a[column];
        const bValue = b[column];

        // Handle null/undefined values
        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return isDescending ? 1 : -1;
        if (bValue == null) return isDescending ? -1 : 1;

        // Optimized comparison
        let result = 0;
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          result = aValue - bValue;
        } else if (column === 'tradeNo') {
          // Special handling for tradeNo - convert to numbers for proper numerical sorting
          const aNum = Number(aValue) || 0;
          const bNum = Number(bValue) || 0;
          result = aNum - bNum;
        } else {
          result = String(aValue).localeCompare(String(bValue));
        }

        return isDescending ? -result : result;
      });
    }

    // Cache the result
    calculationCache.current.set('processedTrades', filtered);
    lastCalculationHash.current = currentHash;

    return filtered;
  }, [trades, searchQuery, statusFilter, sortDescriptor, globalFilter, accountingMethod]);

  // Memory usage monitor
  React.useEffect(() => {
    const checkMemoryUsage = () => {
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        const usedMB = memInfo.usedJSHeapSize / 1024 / 1024;
        const limitMB = memInfo.jsHeapSizeLimit / 1024 / 1024;

        if (usedMB > limitMB * 0.8) { // If using more than 80% of available memory

          // Force garbage collection if available
          if (window.gc) {
            try {
              window.gc();

            } catch (error) {

            }
          }
        }
      }
    };

    const interval = setInterval(checkMemoryUsage, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, []);

  // Performance optimization: Cache for expensive calculations
  const tradesCache = React.useRef(new Map<string, Trade[]>());
  const settingsCache = React.useRef<any>(null);
  const lastLoadTime = React.useRef<number>(0);

  // Load from Supabase with improved cache management to prevent duplicates
  React.useEffect(() => {
    const loadData = async () => {
      const startTime = performance.now();

      // CRITICAL FIX: Re-enable smart caching to prevent 247,163 SELECT calls
      // Use cache if data is fresh (within 5 minutes)
      const now = Date.now();
      const cacheAge = now - lastLoadTime.current;
      const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

      if (tradesCache.current.has('trades') && cacheAge < CACHE_TTL) {
        console.log(`📋 Using cached trades data (age: ${Math.round(cacheAge/1000)}s)`);
        const cachedTrades = tradesCache.current.get('trades')!;
        setTrades(cachedTrades);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        // Load data in parallel for maximum speed
        const [loadedTrades, settings] = await Promise.all([
          getTradesFromSupabase(),
          settingsCache.current || getTradeSettings()
        ]);

        // Cache settings for future use
        if (settings) {
          settingsCache.current = settings;
        }

        // CRITICAL PERFORMANCE OPTIMIZATION:
        // Skip expensive calculations on initial load - do them in background
        const quickTrades = loadedTrades.length > 0 ?
          recalculateTradesWithCurrentPortfolio(loadedTrades, true) : []; // Skip expensive calculations

        // Extract settings values
        const savedSearchQuery = settings?.search_query || '';
        const savedStatusFilter = settings?.status_filter || '';

        // Set state immediately with quick calculations for instant UI
        setTrades(quickTrades);
        setSearchQuery(savedSearchQuery);
        setStatusFilter(savedStatusFilter);
        setSortDescriptor(settings?.sort_descriptor || { column: 'tradeNo', direction: 'ascending' });
        setVisibleColumns(settings?.visible_columns || DEFAULT_VISIBLE_COLUMNS);

        // CRITICAL FIX: Re-enable caching to prevent excessive database calls
        tradesCache.current.set('trades', quickTrades);
        lastLoadTime.current = now;

        // Mark as loaded immediately for fast UI
        setIsLoading(false);

        // BACKGROUND PROCESSING: Do full calculations after UI is ready
        setTimeout(async () => {
          if (loadedTrades.length > 0) {
            const fullyCalculatedTrades = recalculateTradesWithCurrentPortfolio(loadedTrades, false);
            setTrades(fullyCalculatedTrades);
            // CRITICAL FIX: Cache fully calculated trades
            tradesCache.current.set('trades', fullyCalculatedTrades);
            lastLoadTime.current = Date.now();
          }
        }, 100); // Small delay to let UI render first

      } catch (error) {
        setTrades([]);
      }
    };

    loadData();
  }, []); // Empty dependency array means it runs only once on mount.

  // Save trade settings to IndexedDB
  React.useEffect(() => {
    if (!isLoading) {
      const settings = {
        search_query: searchQuery,
        status_filter: statusFilter,
        sort_descriptor: sortDescriptor,
        visible_columns: visibleColumns
      };
      saveTradeSettings(settings);
    }
  }, [searchQuery, statusFilter, sortDescriptor, visibleColumns, isLoading]);

  // Auto-save removed - all data is saved directly to Supabase

  // Recalculate trades when accounting method changes (optimized to prevent excessive re-renders)
  React.useEffect(() => {
    // Only recalculate if accounting method actually changed
    if (prevAccountingMethodRef.current !== accountingMethod && !isLoading && trades.length > 0) {

      // CRITICAL FIX: Use protected recalculation for accounting method changes
      const timeoutId = setTimeout(() => {
        setTrades(currentTrades => {
          return safeRecalculateTradesWithLock(currentTrades, false, 'accounting-method-change');
        });
      }, 150); // Slightly increased delay to reduce frequency

      // Update the ref to track the new accounting method
      prevAccountingMethodRef.current = accountingMethod;

      return () => clearTimeout(timeoutId);
    }
  }, [accountingMethod]); // Only depend on accounting method to avoid circular dependencies

  const addTrade = React.useCallback(async (trade: Trade) => {
    // Ensure trade has a valid UUID before tracking
    if (!trade.id || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(trade.id)) {
      console.error('❌ Cannot add trade with invalid ID:', trade.id);
      return;
    }

    // Track this as a new trade for incremental save
    trackTradeModification(trade.id);

    // CRITICAL FIX: Update chart blob tradeIds if this trade has chart attachments
    if (trade.chartAttachments && (trade.chartAttachments.beforeEntry || trade.chartAttachments.afterExit)) {
      try {
        // Update beforeEntry blob if exists
        if (trade.chartAttachments.beforeEntry?.storage === 'blob' && trade.chartAttachments.beforeEntry.blobId) {
          await SupabaseService.updateChartImageBlobTradeId(trade.chartAttachments.beforeEntry.blobId, trade.id);
          }

        // Update afterExit blob if exists
        if (trade.chartAttachments.afterExit?.storage === 'blob' && trade.chartAttachments.afterExit.blobId) {
          await SupabaseService.updateChartImageBlobTradeId(trade.chartAttachments.afterExit.blobId, trade.id);
          }
      } catch (error) {
        }
    }

    setTrades(prev => {
      // Add new trade to the array
      const combinedTrades = [...prev, trade];

      // Sort all trades by date to ensure proper chronological order (with safe date parsing)
      combinedTrades.sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);

        // Handle invalid dates by putting them at the end
        if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) return 0;
        if (isNaN(dateA.getTime())) return 1;
        if (isNaN(dateB.getTime())) return -1;

        return dateA.getTime() - dateB.getTime();
      });

      // CRITICAL FIX: Deduplicate trades before reassigning numbers to prevent duplicates
      const uniqueTrades = new Map<string, Trade>();
      combinedTrades.forEach(trade => {
        const key = `${trade.id}`;
        if (!uniqueTrades.has(key)) {
          uniqueTrades.set(key, trade);
        }
      });

      // FIXED: Convert back to array and preserve existing trade numbers when possible
      const deduplicatedTrades = Array.from(uniqueTrades.values());

      // Sort by date to maintain chronological order for numbering
      deduplicatedTrades.sort((a, b) => {
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        return dateA - dateB;
      });

      // Removed automatic trade renumbering logic - preserve user-assigned trade numbers as-is

      // Use deduplicated trades instead of combined trades
      const finalTrades = deduplicatedTrades;

      // Use the memoized recalculation helper
      const newTrades = recalculateTradesWithCurrentPortfolio(finalTrades);

      // CRITICAL FIX: Use debounced incremental save for new trades instead of bulk save
      // This will trigger the incremental save logic which uses SupabaseService.saveTrade()
      // SECURITY FIX: Remove forceFullSave parameter completely

      // Use setTimeout to ensure state is updated before save
      setTimeout(() => {
        saveTradesToSupabase(newTrades).then(success => {
          if (!success) {
            console.warn('⚠️ Save failed for new trade');
          }
        }).catch(error => {
          console.error('❌ Save error for new trade:', error);
        });
      }, 100); // Small delay to ensure state consistency

      return newTrades;
    });
  }, [recalculateTradesWithCurrentPortfolio]); // Dependency on the memoized helper

  // Debounced update function to prevent excessive recalculations
  const debouncedRecalculateRef = React.useRef<NodeJS.Timeout | null>(null);
  const pendingUpdatesRef = React.useRef<Map<string, Trade>>(new Map());
  const updateCallbacksRef = React.useRef<Map<string, () => void>>(new Map());

  const updateTrade = React.useCallback(async (updatedTrade: Trade, onComplete?: () => void) => {
    // OPTIMIZED: Minimal logging for production performance

    // Track this trade as modified for incremental save
    trackTradeModification(updatedTrade.id);

    // CRITICAL FIX: Update chart blob tradeIds if this trade has chart attachments
    if (updatedTrade.chartAttachments && (updatedTrade.chartAttachments.beforeEntry || updatedTrade.chartAttachments.afterExit)) {
      try {
        // Update beforeEntry blob if exists
        if (updatedTrade.chartAttachments.beforeEntry?.storage === 'blob' && updatedTrade.chartAttachments.beforeEntry.blobId) {
          await SupabaseService.updateChartImageBlobTradeId(updatedTrade.chartAttachments.beforeEntry.blobId, updatedTrade.id);
          }

        // Update afterExit blob if exists
        if (updatedTrade.chartAttachments.afterExit?.storage === 'blob' && updatedTrade.chartAttachments.afterExit.blobId) {
          await SupabaseService.updateChartImageBlobTradeId(updatedTrade.chartAttachments.afterExit.blobId, updatedTrade.id);
          }
      } catch (error) {
        }
    }

    // Store pending update
    pendingUpdatesRef.current.set(updatedTrade.id, updatedTrade);
    // Store callback if provided
    if (onComplete) {
      updateCallbacksRef.current.set(updatedTrade.id, onComplete);
    }

    // Clear existing debounce timer
    if (debouncedRecalculateRef.current) {
      clearTimeout(debouncedRecalculateRef.current);
      }

    // Schedule debounced recalculation
    debouncedRecalculateRef.current = setTimeout(() => {
      // Get all pending updates and callbacks
      const pendingUpdates = Array.from(pendingUpdatesRef.current.values());
      const callbacks = Array.from(updateCallbacksRef.current.values());
      // Clear pending updates and callbacks
      pendingUpdatesRef.current.clear();
      updateCallbacksRef.current.clear();

      // Apply all pending updates and recalculate
      setTrades(currentTrades => {
        const updatedTrades = currentTrades.map(trade => {
          // CRITICAL FIX: Handle cash basis expanded trade IDs
          // Find pending updates by checking both exact ID match and original ID match
          const pendingUpdate = pendingUpdates.find(update => {
            // Direct match (for accrual basis or exact expanded trade match)
            if (update.id === trade.id) return true;

            // Original ID match (for cash basis expanded trades)
            const originalUpdateId = update.id.includes('_exit_') ? update.id.split('_exit_')[0] : update.id;
            const originalTradeId = trade.id.includes('_exit_') ? trade.id.split('_exit_')[0] : trade.id;

            // Match if both resolve to the same original trade ID
            return originalUpdateId === originalTradeId;
          });

          if (pendingUpdate) {
            // CRITICAL: For cash basis updates, we need to merge the changes into the original tradeiginal trade
            // but preserve the original trade ID (not the expanded ID)
            const updatedTrade = { ...pendingUpdate, id: trade.id };
            return updatedTrade;
          }
          return trade;
        });

        // CRITICAL FIX: Deduplicate trades before recalculation to prevent duplicates
        const uniqueTrades = new Map<string, Trade>();
        updatedTrades.forEach(trade => {
          const key = `${trade.id}`;
          if (!uniqueTrades.has(key)) {
            uniqueTrades.set(key, trade);
          }
        });

        const deduplicatedTrades = Array.from(uniqueTrades.values());
        const recalculatedTrades = recalculateTradesWithCurrentPortfolio(deduplicatedTrades);

        // OPTIMIZED: Minimal logging for save operation
        saveTradesToSupabase(recalculatedTrades);

        // Execute all callbacks after update is complete
        callbacks.forEach(callback => {
          try {
            callback();
          } catch (error) {
            console.error('❌ Callback error:', error);
          }
        });

        return recalculatedTrades;
      });
    }, 200); // Reduced to 200ms to prevent race conditions with user input
  }, [recalculateTradesWithCurrentPortfolio]);

  const deleteTrade = React.useCallback(async (id: string) => {
    // CRITICAL FIX: Handle cash basis expanded trade IDs
    // Extract original trade ID from expanded IDs like "original_id_exit_0"
    const originalTradeId = id.includes('_exit_') ? id.split('_exit_')[0] : id;

    // Track this trade as deleted for incremental save
    trackTradeDeletion(originalTradeId);

    // First, delete associated chart images
    try {
      const { ChartImageService } = await import('../services/chartImageService');
      const chartImagesDeleted = await ChartImageService.deleteTradeChartImages(originalTradeId);
      } catch (error) {
      // Continue with trade deletion even if chart deletion fails
    }

    setTrades(prev => {
      // Find the trade to delete using the original ID
      const tradeToDelete = prev.find(trade => trade.id === originalTradeId);
      if (!tradeToDelete) {
        console.warn('Trade not found for deletion:', originalTradeId);
        return prev; // Return unchanged if trade not found
      }

      // Filter out the trade using the original ID
      const filteredTrades = prev.filter(trade => trade.id !== originalTradeId);
      // Use the memoized recalculation helper
      const newTrades = recalculateTradesWithCurrentPortfolio(filteredTrades);
      // Persist to Supabase
      saveTradesToSupabase(newTrades).then(saveSuccess => {
        });

      return newTrades;
    });
  }, [recalculateTradesWithCurrentPortfolio]);

  // Save operation lock to prevent multiple simultaneous saves
  const savingRef = React.useRef(false);

  // Bulk import function for better performance with optimized calculations
  const bulkImportTrades = React.useCallback((importedTrades: Trade[]) => {
    // CRITICAL FIX: Prevent multiple simultaneous bulk imports
    if (savingRef.current) {
      return;
    }

    savingRef.current = true;
    const startTime = performance.now();

    // Clear cache to prevent duplicate loading issues
    tradesCache.current.clear();
    lastLoadTime.current = 0;

    // Process only the new imported trades for database insertion
    // Don't combine with existing trades to avoid duplication

    // Ensure all imported trades have valid UUIDs
    const validatedTrades = importedTrades.map(trade => {
      const hasValidUUID = trade.id && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(trade.id);
      if (!hasValidUUID) {

      }
      return {
        ...trade,
        id: hasValidUUID ? trade.id : uuidv4()
      };
    });



    // Sort validated trades by date to ensure proper chronological order
    validatedTrades.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);

      // Handle invalid dates by putting them at the end
      if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) return 0;
      if (isNaN(dateA.getTime())) return 1;
      if (isNaN(dateB.getTime())) return -1;

      return dateA.getTime() - dateB.getTime();
    });

    // Removed automatic trade numbering for imports - preserve user-assigned trade numbers

    // First pass: Skip expensive calculations for faster import
    const quickProcessedTrades = recalculateTradesWithCurrentPortfolio(validatedTrades, true);

      // Disable tracking for bulk operations
      disableTracking();

      // Use bulk import method that appends instead of replacing
      SupabaseService.bulkImportTrades(quickProcessedTrades).then(async (success) => {
        if (success) {
          // Clear all caches to ensure fresh data
          tradesCache.current.clear();
          calculationCache.current.clear();
          lastLoadTime.current = 0;

          // Force reload trades from database to get accurate count

          await clearCacheAndReload();
        }
      }).finally(() => {
        // Always reset the saving flag
        savingRef.current = false;
        // Re-enable tracking after bulk operation
        enableTracking();
      });

  }, [recalculateTradesWithCurrentPortfolio]);

  // CRITICAL FIX: Add function to clear all caches and prevent duplicates
  const clearCacheAndReload = React.useCallback(async () => {
    // Clear all caches
    tradesCache.current.clear();
    calculationCache.current.clear();
    lastLoadTime.current = 0;
    lastCalculationHash.current = '';

    // Clear browser caches if available
    if (typeof window !== 'undefined') {
      (window as any).tradeCache = undefined;
      (window as any).portfolioCache = undefined;
      (window as any).settingsCache = undefined;
    }

    // Force reload from Supabase
    setIsLoading(true);
    try {
      const freshTrades = await getTradesFromSupabase();
      const recalculatedTrades = recalculateTradesWithCurrentPortfolio(freshTrades, false);
      setTrades(recalculatedTrades);
    } catch (error) {
      setTrades([]);
    } finally {
      setIsLoading(false);
    }
  }, [recalculateTradesWithCurrentPortfolio]);

  const clearAllTrades = React.useCallback(async () => {
    const success = await clearAllTradeAndSettingsData();

    if (success) {
      // Reset all React state to initial values
      setTrades([]);
      setSearchQuery('');
      setStatusFilter('');
      setSortDescriptor({ column: 'tradeNo', direction: 'ascending' });
      setVisibleColumns(DEFAULT_VISIBLE_COLUMNS);
      setIsLoading(false);

      // Clear all caches
      tradesCache.current.clear();
      calculationCache.current.clear();
      lastLoadTime.current = 0;
      lastCalculationHash.current = '';

      // Force garbage collection if available (Chrome DevTools)
      if (window.gc) {
        try {
          window.gc();
          } catch (error) {
          }
      }

      // Clear any cached data in memory
      if (typeof window !== 'undefined') {
        // Clear any global variables that might hold trade data
        (window as any).tradeCache = undefined;
        (window as any).portfolioCache = undefined;
        (window as any).settingsCache = undefined;
      }

      return true;
    }

    return false;
  }, []);

  // Helper function to get accounting-aware values for display (FIXED - always calculate)
  const getAccountingAwareValues = React.useCallback((trade: Trade) => {
    // CRITICAL FIX: For cash basis, properly handle expanded trades to get total P/L
    let plRs = 0;
    let realisedAmount = 0;

    if (useCashBasis && trade._expandedTrades && trade._expandedTrades.length > 0) {
      // For cash basis with expanded trades, sum up all exit P/L and values
      plRs = trade._expandedTrades.reduce((sum, expandedTrade) => {
        return sum + calculateTradePL(expandedTrade, true);
      }, 0);

      realisedAmount = trade._expandedTrades.reduce((sum, expandedTrade) => {
        if (expandedTrade._cashBasisExit) {
          const exitValue = expandedTrade._cashBasisExit.qty * expandedTrade._cashBasisExit.price;
          return sum + exitValue;
        }
        return sum;
      }, 0);
    } else {
      // For accrual basis or trades without expanded data, use the standard calculation
      plRs = calculateTradePL(trade, useCashBasis);
      realisedAmount = trade.realisedAmount || (trade.exitedQty * trade.avgExitPrice) || 0;
    }

    // Calculate portfolio impact based on the calculated P/L
    const currentPortfolioSize = getPortfolioSize ?
      (() => {
        // CRITICAL FIX: For cash basis, use latest exit date to get portfolio size
        // For accrual basis, use entry date
        let relevantDate = trade.date; // Default to entry date

        if (useCashBasis && (trade.positionStatus === 'Closed' || trade.positionStatus === 'Partial')) {
          // Get the latest exit date for cash basis
          const exitDates = [
            trade.exit1Date,
            trade.exit2Date,
            trade.exit3Date
          ].filter(date => date && date.trim() !== '').sort();

          if (exitDates.length > 0) {
            relevantDate = exitDates[exitDates.length - 1]; // Use latest exit date
          }
        }

        const tradeDate = new Date(relevantDate);
        const rawMonth = tradeDate.toLocaleString('default', { month: 'short' });
        const month = normalizeMonthName(rawMonth); // CRITICAL FIX: Normalize Sept -> Sep
        const year = tradeDate.getFullYear();
        return getPortfolioSize(month, year);
      })() : portfolioSize;

    const pfImpact = currentPortfolioSize > 0 ? (plRs / currentPortfolioSize) * 100 : 0;

    return {
      plRs,
      realisedAmount,
      pfImpact,
    };
  }, [useCashBasis, calculateTradePL, getPortfolioSize, portfolioSize]);

  // Helper function to group expanded trades for display
  const groupTradesForDisplay = React.useCallback((expandedTrades: Trade[]) => {
    if (!useCashBasis) return expandedTrades;

    const groupedMap = new Map<string, Trade>();
    const expandedTradesMap = new Map<string, Trade[]>();

    expandedTrades.forEach(trade => {
      const originalId = trade.id.split('_exit_')[0];

      if (trade._cashBasisExit) {
        // This is an expanded trade for cash basis
        if (!expandedTradesMap.has(originalId)) {
          expandedTradesMap.set(originalId, []);
        }
        expandedTradesMap.get(originalId)!.push(trade);
      } else {
        // This is an original trade (open position or single exit)
        groupedMap.set(originalId, trade);
      }
    });

    // Merge expanded trades back into single display entries
    expandedTradesMap.forEach((expandedTrades, originalId) => {
      if (expandedTrades.length === 0) return;

      // Use the first expanded trade as base and aggregate the cash basis data
      const baseTrade = expandedTrades[0];
      const aggregatedTrade: Trade = {
        ...baseTrade,
        id: originalId, // Use original ID for display
        // Aggregate P/L from all exits for display
        plRs: expandedTrades.reduce((sum, t) => sum + (calculateTradePL(t, true) || 0), 0),
        // Keep the latest exit date for sorting
        _cashBasisExit: expandedTrades.reduce((latest, current) => {
          if (!latest || !current._cashBasisExit) return current._cashBasisExit;
          if (!latest.date || !current._cashBasisExit.date) return latest;
          return new Date(current._cashBasisExit.date) > new Date(latest.date) ? current._cashBasisExit : latest;
        }, expandedTrades[0]._cashBasisExit),
        // Store expanded trades for backend calculations
        _expandedTrades: expandedTrades
      };

      groupedMap.set(originalId, aggregatedTrade);
    });

    return Array.from(groupedMap.values());
  }, [useCashBasis, calculateTradePL]);

  const filteredTrades = React.useMemo(() => {
    let result = [...trades];

    // For cash basis, we need to handle trade filtering differently
    // Instead of filtering trades, we need to expand trades with multiple exits
    if (useCashBasis) {
      // Expand trades with multiple exits into separate entries for cash basis
      const expandedTrades: Trade[] = [];
      const debugExpandedMap: Record<string, Trade[]> = {};

      result.forEach(trade => {
        if (trade.positionStatus === 'Closed' || trade.positionStatus === 'Partial') {
          // Get all exits for this trade
          const exits = [
            { date: trade.exit1Date, qty: trade.exit1Qty || 0, price: trade.exit1Price || 0 },
            { date: trade.exit2Date, qty: trade.exit2Qty || 0, price: trade.exit2Price || 0 },
            { date: trade.exit3Date, qty: trade.exit3Qty || 0, price: trade.exit3Price || 0 }
          ].filter(exit => exit.date && exit.date.trim() !== '' && exit.qty > 0);

          if (exits.length > 0) {
            // Create a trade entry for each exit (for cash basis)
            exits.forEach((exit, exitIndex) => {
              const expandedTrade: Trade = {
                ...trade,
                id: trade.id + '_exit_' + exitIndex,
                _cashBasisExit: {
                  date: exit.date,
                  qty: exit.qty,
                  price: exit.price
                }
              };
              expandedTrades.push(expandedTrade);
              if (!debugExpandedMap[trade.id]) debugExpandedMap[trade.id] = [];
              debugExpandedMap[trade.id].push(expandedTrade);
            });
          } else {

            // Fallback: if no individual exit data, use the original trade
            expandedTrades.push(trade);
            if (!debugExpandedMap[trade.id]) debugExpandedMap[trade.id] = [];
            debugExpandedMap[trade.id].push(trade);
          }
        } else {
          // For open positions, include as-is
          expandedTrades.push(trade);
        }
      });

      // CRITICAL FIX: Apply global filter to expanded trades BEFORE grouping
      // This ensures trades with multiple exits are properly filtered by each exit date
      const filteredExpandedTrades = expandedTrades.filter(trade => {
        const relevantDate = getTradeDateForAccounting(trade, useCashBasis);
        return isInGlobalFilter(relevantDate, globalFilter);
      });

      // Group filtered expanded trades for display while preserving backend calculations
      result = groupTradesForDisplay(filteredExpandedTrades);
    } else {
      // Apply global filter using accounting method-aware date for accrual basis
      result = result.filter(trade => {
        const relevantDate = getTradeDateForAccounting(trade, useCashBasis);
        return isInGlobalFilter(relevantDate, globalFilter);
      });
    }

    // Apply search filter
    if (searchQuery) {
      const lowerQuery = searchQuery.toLowerCase();
      result = result.filter(trade =>
        trade.name.toLowerCase().includes(lowerQuery) ||
        trade.setup.toLowerCase().includes(lowerQuery) ||
        trade.tradeNo.toLowerCase().includes(lowerQuery)
      );
    }

    // Apply status filter
    if (statusFilter) {
      result = result.filter(trade => trade.positionStatus === statusFilter);
    }

    // Apply sorting
    if (sortDescriptor.column && sortDescriptor.direction) {
      result.sort((a, b) => {
        const aValue = a[sortDescriptor.column as keyof Trade];
        const bValue = b[sortDescriptor.column as keyof Trade];

        let comparison = 0;
        // Handle different data types for sorting
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          comparison = aValue - bValue;
        } else if (sortDescriptor.column === 'tradeNo') {
          // Special handling for tradeNo - convert to numbers for proper numerical sorting
          const aNum = Number(aValue) || 0;
          const bNum = Number(bValue) || 0;
          comparison = aNum - bNum;
        } else if (typeof aValue === 'string' && typeof bValue === 'string') {
          // Special handling for date strings if your date format is sortable as string
          if (sortDescriptor.column === 'date' || String(sortDescriptor.column).endsWith('Date')) {
            comparison = new Date(aValue).getTime() - new Date(bValue).getTime();
          } else {
            comparison = aValue.localeCompare(bValue);
          }
        } else if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
          comparison = (aValue === bValue) ? 0 : aValue ? -1 : 1;
        } else {
          // Fallback for other types or mixed types (treat as strings)
          const StringA = String(aValue !== null && aValue !== undefined ? aValue : "");
          const StringB = String(bValue !== null && bValue !== undefined ? bValue : "");
          comparison = StringA.localeCompare(StringB);
        }

        // For cash basis, add secondary sorting to handle expanded trades properly
        if (useCashBasis && comparison === 0) {
          // If primary sort values are equal, sort by exit date for cash basis
          const aExitDate = a._cashBasisExit?.date || a.date || '';
          const bExitDate = b._cashBasisExit?.date || b.date || '';

          if (aExitDate && bExitDate) {
            const aTime = new Date(aExitDate).getTime();
            const bTime = new Date(bExitDate).getTime();
            comparison = aTime - bTime;
          }
        }

        return sortDescriptor.direction === "ascending" ? comparison : -comparison;
      });
    }

    // CRITICAL FIX: Calculate cumulative PF based on CHRONOLOGICAL order, not display order
    // This ensures cumulative values follow the actual sequence of trades regardless of sorting

    // Step 1: Create a chronologically sorted copy for cumulative calculation
    const chronologicalTrades = [...result].sort(getChronologicalSortComparator());

    // Step 2: Calculate cumulative PF in chronological order
    let runningChronologicalCummPf = 0;
    const cummPfMap = new Map<string, number>(); // Map trade ID to cumulative PF

    chronologicalTrades.forEach((trade) => {
      // For cash basis grouped trades, calculate PF impact from expanded trades if available
      let currentPfImpact = 0;

      if (useCashBasis && trade._expandedTrades && trade._expandedTrades.length > 0) {
        // Calculate total PL impact from all expanded trades
        const totalPL = trade._expandedTrades.reduce((sum, expandedTrade) => {
          return sum + calculateTradePL(expandedTrade, true);
        }, 0);

        // Use the correct portfolio size for cash basis
        let portfolioSize = 100000; // Default fallback

        if (getPortfolioSize && trade._expandedTrades.length > 0) {
          // Find the latest exit date among all expanded trades
          const latestExit = trade._expandedTrades.reduce((latest, current) => {
            if (!latest || !current._cashBasisExit) return current._cashBasisExit;
            if (!latest.date || !current._cashBasisExit.date) return latest;
            return new Date(current._cashBasisExit.date) > new Date(latest.date) ? current._cashBasisExit : latest;
          }, trade._expandedTrades[0]._cashBasisExit);

          if (latestExit && latestExit.date) {
            const exitDate = new Date(latestExit.date);
            const rawMonth = exitDate.toLocaleString('default', { month: 'short' });
            const month = normalizeMonthName(rawMonth); // CRITICAL FIX: Normalize Sept -> Sep
            const year = exitDate.getFullYear();
            portfolioSize = getPortfolioSize(month, year) || 100000;
          }
        }

        currentPfImpact = portfolioSize > 0 ? (totalPL / portfolioSize) * 100 : 0;
      } else {
        // Use cached values or fallback calculation
        currentPfImpact = useCashBasis
          ? (trade._cashPfImpact ?? 0)
          : (trade._accrualPfImpact ?? trade.pfImpact ?? 0);
      }

      // Only include PF Impact from closed/partial trades in cumulative calculation
      if (trade.positionStatus !== 'Open') {
        runningChronologicalCummPf += currentPfImpact;
      }

      // Store the cumulative PF for this trade
      cummPfMap.set(trade.id, runningChronologicalCummPf);
    });

    // Step 3: Apply the chronologically calculated cumulative PF to display-ordered trades
    result = result.map((trade) => ({
      ...trade,
      cummPf: cummPfMap.get(trade.id) || 0 // Use chronologically calculated cumulative PF
    }));

    return result;
  }, [trades, globalFilter, searchQuery, statusFilter, sortDescriptor, useCashBasis]);

  return {
    trades: filteredTrades, // Filtered and expanded trades for display
    originalTrades: trades, // Original trades for unrealized P/L calculation
    addTrade,
    updateTrade,
    deleteTrade,
    bulkImportTrades,
    isLoading,
    isRecalculating,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    sortDescriptor,
    setSortDescriptor,
    visibleColumns,
    setVisibleColumns,
    clearAllTrades,
    clearCacheAndReload, // CRITICAL FIX: New function to clear cache and prevent duplicates
    getAccountingAwareValues // Helper for getting accounting-aware display values
  };
};
