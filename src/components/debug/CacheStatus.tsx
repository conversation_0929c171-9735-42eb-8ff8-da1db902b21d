import React, { useState, useEffect } from 'react';
import { Card, CardBody, Button, Chip } from '@heroui/react';
import { Icon } from '@iconify/react';
import { CachePreloader } from '../../services/cachePreloader';
import { SupabaseService } from '../../services/supabaseService';

interface CacheStatusProps {
  isVisible?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

export const CacheStatus: React.FC<CacheStatusProps> = ({ 
  isVisible = false, 
  position = 'bottom-right' 
}) => {
  const [stats, setStats] = useState<any>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  // Update stats every 5 seconds
  useEffect(() => {
    if (!isVisible) return;

    const updateStats = () => {
      const preloaderStats = CachePreloader.getStats();
      setStats(preloaderStats);
    };

    updateStats();
    const interval = setInterval(updateStats, 5000);

    return () => clearInterval(interval);
  }, [isVisible]);

  if (!isVisible || !stats) return null;

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  const getCacheHealthColor = () => {
    const { cacheStats } = stats;
    const totalCacheEntries = cacheStats.tradesCache + cacheStats.miscDataCache +
                             cacheStats.monthlyPerformanceCache + cacheStats.chartViewerCache +
                             cacheStats.drawdownCache + cacheStats.analyticsCache;

    if (totalCacheEntries > 15) return 'success';
    if (totalCacheEntries > 8) return 'warning';
    return 'danger';
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-50`}>
      <Card className="bg-background/80 backdrop-blur-md border border-divider shadow-lg">
        <CardBody className="p-1">
          <div className="flex items-center gap-1">
            <Icon
              icon="lucide:database"
              className={`w-2.5 h-2.5 ${getCacheHealthColor() === 'success' ? 'text-success' : getCacheHealthColor() === 'warning' ? 'text-warning' : 'text-danger'}`}
            />

            <Chip
              size="sm"
              color={getCacheHealthColor()}
              variant="flat"
              className="text-[10px] px-1 py-0 h-4 min-h-4"
            >
              {stats.cacheStats.tradesCache + stats.cacheStats.miscDataCache +
               stats.cacheStats.monthlyPerformanceCache + stats.cacheStats.chartViewerCache +
               stats.cacheStats.drawdownCache + stats.cacheStats.analyticsCache}
            </Chip>

            <Button
              isIconOnly
              size="sm"
              variant="light"
              onPress={() => setIsExpanded(!isExpanded)}
              className="w-4 h-4 min-w-4 min-h-4"
            >
              <Icon
                icon={isExpanded ? "lucide:chevron-up" : "lucide:chevron-down"}
                className="w-2 h-2"
              />
            </Button>
          </div>

          {isExpanded && (
            <div className="mt-3 space-y-2 text-xs">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <div className="text-foreground/60">Strategy:</div>
                  <div className="font-medium capitalize">{stats.strategy}</div>
                </div>
                <div>
                  <div className="text-foreground/60">Preload Time:</div>
                  <div className="font-medium">{formatTime(stats.totalTimeMs)}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <div className="text-foreground/60">Trades Cache:</div>
                  <div className="font-medium">{stats.cacheStats.tradesCache} entries</div>
                </div>
                <div>
                  <div className="text-foreground/60">Misc Cache:</div>
                  <div className="font-medium">{stats.cacheStats.miscDataCache} entries</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <div className="text-foreground/60">Monthly Perf:</div>
                  <div className="font-medium">{stats.cacheStats.monthlyPerformanceCache} entries</div>
                </div>
                <div>
                  <div className="text-foreground/60">Chart Viewer:</div>
                  <div className="font-medium">{stats.cacheStats.chartViewerCache} entries</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <div className="text-foreground/60">Drawdown:</div>
                  <div className="font-medium">{stats.cacheStats.drawdownCache} entries</div>
                </div>
                <div>
                  <div className="text-foreground/60">Analytics:</div>
                  <div className="font-medium">{stats.cacheStats.analyticsCache} entries</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <div className="text-foreground/60">Success Rate:</div>
                  <div className="font-medium">
                    {stats.totalPreloads > 0 
                      ? Math.round((stats.successfulPreloads / stats.totalPreloads) * 100)
                      : 0
                    }%
                  </div>
                </div>
                <div>
                  <div className="text-foreground/60">Total Preloads:</div>
                  <div className="font-medium">{stats.totalPreloads}</div>
                </div>
              </div>

              <div className="flex gap-1 pt-2">
                <Button
                  size="sm"
                  variant="flat"
                  color="primary"
                  onPress={() => {
                    CachePreloader.warmCache([
                      'trades', 'analytics', 'monthly-performance',
                      'drawdown', 'performance', 'risk', 'metrics'
                    ]);
                  }}
                  className="text-xs h-6"
                >
                  <Icon icon="lucide:refresh-cw" className="w-3 h-3" />
                  Warm All
                </Button>
                
                <Button
                  size="sm"
                  variant="flat"
                  color="warning"
                  onPress={() => {
                    SupabaseService.clearAllCaches();
                    setStats(CachePreloader.getStats());
                  }}
                  className="text-xs h-6"
                >
                  <Icon icon="lucide:trash-2" className="w-3 h-3" />
                  Clear
                </Button>
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

// Hook for easy cache status monitoring
export const useCacheStatus = () => {
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    const updateStats = () => {
      setStats(CachePreloader.getStats());
    };

    updateStats();
    const interval = setInterval(updateStats, 10000); // Update every 10 seconds

    return () => clearInterval(interval);
  }, []);

  return {
    stats,
    isHealthy: stats ? (
      stats.cacheStats.tradesCache + stats.cacheStats.miscDataCache +
      stats.cacheStats.monthlyPerformanceCache + stats.cacheStats.chartViewerCache +
      stats.cacheStats.drawdownCache + stats.cacheStats.analyticsCache
    ) > 8 : false,
    clearCache: () => SupabaseService.clearAllCaches(),
    warmCache: (types: string[]) => CachePreloader.warmCache(types)
  };
};

// Development-only cache monitor
export const CacheMonitor: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  // Only show in development
  useEffect(() => {
    const isDev = import.meta.env.DEV;
    setIsVisible(isDev);
  }, []);

  // Toggle with keyboard shortcut (Ctrl+Shift+C)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return <CacheStatus isVisible={isVisible} position="bottom-left" />;
};
