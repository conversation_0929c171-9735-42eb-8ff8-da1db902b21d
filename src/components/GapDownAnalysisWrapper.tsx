import React, { useState } from 'react';
import { GapDownAnalysisButton } from './GapDownAnalysisButton';
import GapDownAnalysisModal from './GapDownAnalysisModalNew';
import { useTrades } from '../hooks/use-trades';
import { useTruePortfolio } from '../utils/TruePortfolioContext';

export const GapDownAnalysisWrapper: React.FC = () => {
  const [isGapDownAnalysisOpen, setIsGapDownAnalysisOpen] = useState(false);
  
  // Get trades data for gap down analysis - now inside provider scope
  const { trades } = useTrades();
  const { portfolioSize, getPortfolioSize } = useTruePortfolio();

  return (
    <>
      {/* Gap Down Analysis Button - positioned on the right */}
      <GapDownAnalysisButton
        trades={trades}
        onOpenAnalysis={() => setIsGapDownAnalysisOpen(true)}
        position="bottom-right"
      />

      {/* Gap Down Analysis Modal */}
      <GapDownAnalysisModal
        isOpen={isGapDownAnalysisOpen}
        onOpenChange={setIsGapDownAnalysisOpen}
        trades={trades}
        portfolioSize={portfolioSize}
        getPortfolioSize={getPortfolioSize}
      />
    </>
  );
};
