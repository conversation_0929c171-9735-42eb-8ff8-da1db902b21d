import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON>dal<PERSON>eader,
  <PERSON>dalBody,
  <PERSON>dal<PERSON>ooter,
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Select,
  SelectItem,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Divider,
  Progress
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import { Trade } from "../types/trade";
import {
  calcPortfolioGapDownAnalysis,
  getGapDownScenarios,
  PortfolioGapDownAnalysis,
  GapDownScenario,
  isRiskyPosition
} from "../utils/tradeCalculations";
import { formatCurrency, formatPercentage } from "../utils/formatters";

interface GapDownAnalysisModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  trades: Trade[];
  portfolioSize: number;
  getPortfolioSize?: (month: string, year: number) => number;
}

export const GapDownAnalysisModal: React.FC<GapDownAnalysisModalProps> = ({
  isOpen,
  onOpenChange,
  trades,
  portfolioSize,
  getPortfolioSize
}) => {
  const [selectedScenario, setSelectedScenario] = useState<string>("5");
  const scenarios = getGapDownScenarios();

  // Filter for risky open positions only (SL only, no TSL)
  const riskyOpenTrades = useMemo(() =>
    trades.filter(t =>
      (t.positionStatus === 'Open' || t.positionStatus === 'Partial') &&
      t.openQty > 0 &&
      isRiskyPosition(t)
    ), [trades]
  );

  // Count risk-free positions for display
  const riskFreePositions = useMemo(() =>
    trades.filter(t =>
      (t.positionStatus === 'Open' || t.positionStatus === 'Partial') &&
      t.openQty > 0 &&
      !isRiskyPosition(t)
    ), [trades]
  );

  // Calculate analysis based on selected scenario
  const analysis = useMemo(() => {
    const gapDownPercentage = parseFloat(selectedScenario);
    return calcPortfolioGapDownAnalysis(riskyOpenTrades, gapDownPercentage, portfolioSize, getPortfolioSize);
  }, [riskyOpenTrades, selectedScenario, portfolioSize, getPortfolioSize]);

  const getRiskColor = (impact: number, isIncremental: boolean = false) => {
    if (isIncremental && impact < 0) return "success"; // Negative incremental is good
    if (impact < 2) return "success";
    if (impact < 5) return "warning";
    return "danger";
  };

  const getRiskLevel = (impact: number, isIncremental: boolean = false) => {
    if (isIncremental && impact < 0) return "Protected"; // Negative incremental means protected
    if (impact < 2) return "Low";
    if (impact < 5) return "Moderate";
    if (impact < 10) return "High";
    return "Extreme";
  };

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      size="5xl"
      scrollBehavior="inside"
      classNames={{
        base: "transform-gpu backdrop-blur-sm",
        wrapper: "transform-gpu",
        backdrop: "bg-black/40",
        closeButton: "text-foreground/60 hover:bg-white/10"
      }}
      backdrop="blur"
    >
      <ModalContent className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-2xl border border-gray-200 dark:border-gray-700 shadow-2xl max-h-[90vh]">
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1 border-b border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-900/80">
              <div className="flex items-center gap-3">
                <Icon icon="lucide:trending-down" className="text-2xl text-danger-500" />
                <div>
                  <h2 className="text-xl font-bold">What If Gap Down Analysis</h2>
                  <p className="text-sm text-foreground-600">
                    Analyze portfolio impact when risky positions (SL only) gap down below stop loss levels
                  </p>
                </div>
              </div>
            </ModalHeader>

            <ModalBody className="p-6">
              {riskyOpenTrades.length === 0 ? (
                <div className="text-center py-8">
                  <Icon icon="lucide:party-popper" className="text-4xl text-success-400 mb-4" />
                  <h3 className="text-xl font-bold text-success-600 mb-2">🎉 Hurray! No Risky Positions!</h3>
                  <p className="text-lg text-success-700 dark:text-success-300 mb-4 font-medium">
                    All open positions are risk-free at the moment!
                  </p>

                  {riskFreePositions.length > 0 ? (
                    <div className="bg-gradient-to-r from-success-50 to-emerald-50 dark:from-success-900/20 dark:to-emerald-900/20 p-6 rounded-lg border border-success-200 dark:border-success-800">
                      <h4 className="font-bold text-success-800 dark:text-success-200 mb-3 text-lg">
                        🛡️ {riskFreePositions.length} Risk-Free Position{riskFreePositions.length > 1 ? 's' : ''}
                      </h4>
                      <p className="text-success-700 dark:text-success-300 mb-4">
                        Excellent risk management! All your open positions have TSL protection. In gap down scenarios, you'll only lose unrealized gains, not your capital.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {riskFreePositions.slice(0, 6).map((position, idx) => (
                          <div key={idx} className="bg-white dark:bg-gray-800 p-3 rounded border border-success-300 dark:border-success-700">
                            <div className="font-medium text-success-800 dark:text-success-200">
                              {position.name}
                            </div>
                            <div className="text-sm text-success-600 dark:text-success-400">
                              {position.openQty} qty • TSL: ₹{position.tsl}
                            </div>
                          </div>
                        ))}
                        {riskFreePositions.length > 6 && (
                          <div className="text-sm text-success-600 dark:text-success-400 col-span-full">
                            ...and {riskFreePositions.length - 6} more protected positions
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                      <p className="text-blue-700 dark:text-blue-300">
                        You currently have no open positions. Perfect time to plan your next trades with proper risk management!
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Risk Categorization Info */}
                  <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                    <CardBody className="p-4">
                      <div className="flex items-start gap-3">
                        <Icon icon="lucide:info" className="text-blue-600 dark:text-blue-400 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Risk Analysis Scope</h4>
                          <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                            <p><strong>Risky Positions:</strong> SL only or TSL ≤ SL (analyzed here) - {riskyOpenTrades.length} positions</p>
                            <p><strong>Protected Positions:</strong> TSL > SL (excluded) - {riskFreePositions.length} positions</p>
                            <p className="text-xs mt-2 text-blue-600 dark:text-blue-400">
                              TSL positions are excluded as they protect capital - you'll only lose unrealized gains in gap downs, not your initial investment.
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>

                  {/* Scenario Selection */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">Select Gap Down Scenario</h3>
                    </CardHeader>
                    <CardBody>
                      <Select
                        label="Gap Down Percentage"
                        selectedKeys={[selectedScenario]}
                        onSelectionChange={(keys) => {
                          const selected = Array.from(keys)[0] as string;
                          setSelectedScenario(selected);
                        }}
                        className="max-w-xs"
                      >
                        {scenarios.map((scenario) => (
                          <SelectItem key={scenario.gapDownPercentage.toString()} value={scenario.gapDownPercentage.toString()}>
                            {scenario.description}
                          </SelectItem>
                        ))}
                      </Select>
                    </CardBody>
                  </Card>

                  {/* Portfolio Impact Summary */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">Portfolio Impact Summary</h3>
                    </CardHeader>
                    <CardBody>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-content2 rounded-lg">
                          <div className="text-sm text-foreground-600 mb-1">Planned Risk as per Initial Stop</div>
                          <div className="text-xl font-bold text-warning-600">
                            {formatPercentage(analysis.normalPfImpact)}
                          </div>
                          <div className="text-xs text-foreground-500">
                            {formatCurrency(analysis.totalNormalRisk)}
                          </div>
                          <div className="text-xs text-foreground-400 mt-1">
                            Risk if all stops are hit normally
                          </div>
                        </div>
                        <div className="text-center p-4 bg-content2 rounded-lg">
                          <div className="text-sm text-foreground-600 mb-1">
                            Sudden Gap Down Risk
                            {analysis.additionalPfImpact < 0 && (
                              <div className="text-xs text-success-600 font-medium mt-1">
                                You are safe - won't lose anything other than planned gains
                              </div>
                            )}
                          </div>
                          <div className="text-xl font-bold text-danger-600">
                            {formatPercentage(analysis.gapDownPfImpact)}
                          </div>
                          <div className="text-xs text-foreground-500">
                            {formatCurrency(analysis.totalGapDownRisk)}
                          </div>
                          <div className="text-xs text-foreground-400 mt-1">
                            {analysis.additionalPfImpact < 0
                              ? `Your stops protect you even in ${analysis.scenario.gapDownPercentage}% gap down`
                              : `You will lose if positions gap down ${analysis.scenario.gapDownPercentage}%`
                            }
                          </div>
                        </div>
                        <div className="text-center p-4 bg-content2 rounded-lg">
                          <div className="text-sm text-foreground-600 mb-1">Incremental Change</div>
                          <div className={`text-xl font-bold ${analysis.additionalPfImpact >= 0 ? 'text-danger-700' : 'text-success-600'}`}>
                            {analysis.additionalPfImpact >= 0 ? '+' : ''}{formatPercentage(analysis.additionalPfImpact)}
                          </div>
                          <div className="text-xs text-foreground-500">
                            {analysis.totalAdditionalRisk >= 0 ? '+' : ''}{formatCurrency(analysis.totalAdditionalRisk)}
                          </div>
                          <div className="text-xs text-foreground-400 mt-1">
                            {analysis.additionalPfImpact >= 0
                              ? `Additional PF hit of +${formatPercentage(Math.abs(analysis.additionalPfImpact))} (${formatCurrency(Math.abs(analysis.totalAdditionalRisk))} extra loss)`
                              : `Better PF impact by ${formatPercentage(Math.abs(analysis.additionalPfImpact))} (${formatCurrency(Math.abs(analysis.totalAdditionalRisk))} less loss)`
                            }
                          </div>
                        </div>
                      </div>

                      <Divider className="my-4" />

                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm text-foreground-600">Overall Risk Assessment</div>
                          <Chip
                            color={analysis.additionalPfImpact < 0 ? "success" : getRiskColor(analysis.gapDownPfImpact)}
                            variant="flat"
                            size="lg"
                          >
                            {analysis.additionalPfImpact < 0 ? "Well Protected" : getRiskLevel(analysis.gapDownPfImpact)}
                          </Chip>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-foreground-600">Portfolio Size</div>
                          <div className="text-lg font-semibold">
                            {formatCurrency(analysis.portfolioSize)}
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>

                  {/* Individual Trade Analysis */}
                  <Card>
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold">Position-by-Position Analysis</h3>
                        <div className="text-xs text-foreground-500 bg-content2 px-2 py-1 rounded">
                          TSL is always the effective stop when present
                        </div>
                      </div>
                    </CardHeader>
                    <CardBody>
                      <Table aria-label="Gap down analysis by position">
                        <TableHeader>
                          <TableColumn>STOCK</TableColumn>
                          <TableColumn>CURRENT PRICE</TableColumn>
                          <TableColumn>STOP LOSS</TableColumn>
                          <TableColumn>GAP DOWN PRICE</TableColumn>
                          <TableColumn>PLANNED RISK</TableColumn>
                          <TableColumn>SUDDEN GAP RISK</TableColumn>
                          <TableColumn>INCREMENTAL CHANGE</TableColumn>
                          <TableColumn>PF HIT</TableColumn>
                        </TableHeader>
                        <TableBody>
                          {analysis.tradeAnalyses
                            .sort((a, b) => b.additionalPfImpact - a.additionalPfImpact)
                            .map((tradeAnalysis) => (
                            <TableRow key={tradeAnalysis.tradeId}>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{tradeAnalysis.tradeName}</div>
                                  <div className="text-xs text-foreground-500">
                                    {tradeAnalysis.openQty} qty • {tradeAnalysis.buySell}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="font-mono">
                                  ₹{tradeAnalysis.currentPrice.toFixed(2)}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div>
                                  {tradeAnalysis.stopLossType !== 'None' ? (
                                    <>
                                      <div className="font-mono">
                                        ₹{tradeAnalysis.stopLoss.toFixed(2)}
                                      </div>
                                      <div className="text-xs text-foreground-500">
                                        {tradeAnalysis.stopLossType}
                                        {tradeAnalysis.stopLossType === 'TSL' && tradeAnalysis.originalSL > 0 && (
                                          <span className="ml-1 text-foreground-400">
                                            (SL: ₹{tradeAnalysis.originalSL.toFixed(2)})
                                          </span>
                                        )}
                                      </div>
                                    </>
                                  ) : (
                                    <div className="text-foreground-400 text-sm">No Stop</div>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="font-mono text-danger-600">
                                  ₹{tradeAnalysis.gapDownPrice.toFixed(2)}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="font-mono text-warning-600">
                                  {formatCurrency(tradeAnalysis.normalStopLossRisk)}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="font-mono text-danger-600">
                                  {formatCurrency(tradeAnalysis.gapDownRisk)}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="font-mono text-danger-700">
                                  {formatCurrency(tradeAnalysis.additionalRisk)}
                                </div>
                              </TableCell>
                              <TableCell>
                                <Chip
                                  color={tradeAnalysis.additionalPfImpact < 0 ? "success" : getRiskColor(tradeAnalysis.gapDownPfImpact)}
                                  variant="flat"
                                  size="sm"
                                >
                                  {formatPercentage(tradeAnalysis.gapDownPfImpact)}
                                </Chip>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardBody>
                  </Card>
                </div>
              )}
            </ModalBody>

            <ModalFooter className="border-t border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-900/80">
              <Button color="primary" onPress={onClose}>
                Close Analysis
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
