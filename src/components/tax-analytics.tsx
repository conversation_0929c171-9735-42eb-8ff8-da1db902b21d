import React, { use<PERSON><PERSON>back, useMemo, useRef, memo } from "react";
import {
  <PERSON>,
  Card<PERSON>ody,
  CardHeader,
  <PERSON>vider,
  But<PERSON>,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Tooltip
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import { TaxSummaryChart } from "./tax/tax-summary-chart";
import { TaxTable } from "./tax/tax-table";
import { TaxEditModal } from "./tax/tax-edit-modal";
import { useTrades } from "../hooks/use-trades";
import { useAccountingMethod } from "../context/AccountingMethodContext";
import { useGlobalFilter } from "../context/GlobalFilterContext";
import { calculateTradePL } from "../utils/accountingUtils";
import { SupabaseService } from '../services/supabaseService';
import { AuthService } from '../services/authService';

// PERFORMANCE: Optimized data fetching - rely on SupabaseService caching
// Remove redundant local cache since SupabaseService now has proper caching

// Global cache invalidation function
export const invalidateTaxDataCache = async () => {
  try {
    // Get current user ID for targeted cache invalidation
    const userId = await AuthService.getUserId();
    if (userId) {
      SupabaseService.clearMiscDataCache('taxData', userId);
      console.log('🗑️ Tax data cache invalidated for user:', userId);
    }
  } catch (error) {
    // Fallback to clearing all misc data cache
    SupabaseService.clearMiscDataCache();
    console.log('🗑️ Tax data cache invalidated (fallback)');
  }
};

// Listen for tax data updates from other components
const setupTaxDataListener = () => {
  const handleTaxDataUpdate = () => {
    invalidateTaxDataCache();
  };

  // Listen for custom tax data update events
  window.addEventListener('taxDataUpdated', handleTaxDataUpdate);

  return () => {
    window.removeEventListener('taxDataUpdated', handleTaxDataUpdate);
  };
};

const getCachedTaxData = async (): Promise<any> => {
  try {
    // PERFORMANCE: Use SupabaseService caching instead of local cache
    const data = await SupabaseService.getMiscData('taxData') || {};
    return data;
  } catch (error) {
    console.error('❌ Failed to fetch tax data:', error);
    return {};
  }
};

const saveTaxDataOptimized = async (taxData: any): Promise<boolean> => {
  try {
    const success = await SupabaseService.saveMiscData('taxData', taxData);
    if (success) {
      // SupabaseService automatically updates cache
      // Notify other components about the update
      window.dispatchEvent(new CustomEvent('taxDataUpdated'));
      console.log('✅ Tax data saved and cache updated');
    }
    return success;
  } catch (error) {
    console.error('❌ Failed to save tax data:', error);
    return false;
  }
};

// Export function to update charges breakdown data
export const updateChargesBreakdown = async (chargesData: any): Promise<boolean> => {
  try {
    console.log('💰 Updating charges breakdown data:', chargesData);

    // Get existing tax data
    const existingTaxData = await SupabaseService.getMiscData('taxData') || {};

    // Merge charges data with existing tax data
    const updatedTaxData = {
      ...existingTaxData,
      chargesBreakdown: chargesData,
      lastUpdated: new Date().toISOString()
    };

    const success = await SupabaseService.saveMiscData('taxData', updatedTaxData);
    if (success) {
      // Notify other components about the update
      window.dispatchEvent(new CustomEvent('taxDataUpdated'));
      console.log('✅ Charges breakdown updated and cache refreshed');
    }
    return success;
  } catch (error) {
    console.error('❌ Failed to update charges breakdown:', error);
    return false;
  }
};

// MODERN: Sleek Tax Metrics Loader Component
const TaxMetricsLoader: React.FC = memo(() => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.4, ease: "easeOut" }}
      className="flex items-center justify-center py-16 px-4"
    >
      <div className="text-center space-y-6 max-w-sm">
        {/* Modern animated loader with calculator icon */}
        <div className="relative mx-auto w-20 h-20">
          {/* Outer rotating ring */}
          <motion.div
            className="absolute inset-0 rounded-full border-2 border-primary/20"
            animate={{ rotate: 360 }}
            transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
          />

          {/* Inner counter-rotating ring */}
          <motion.div
            className="absolute inset-3 rounded-full border-2 border-transparent border-t-primary border-r-primary"
            animate={{ rotate: -360 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
          />

          {/* Pulsing center background */}
          <motion.div
            className="absolute inset-6 rounded-full bg-primary/10"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />

          {/* Calculator icon with subtle animation */}
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            animate={{
              scale: [1, 1.05, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Icon
              icon="lucide:calculator"
              className="w-7 h-7 text-primary"
            />
          </motion.div>
        </div>

        {/* Content with staggered animations */}
        <div className="space-y-4">
          <motion.h3
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.4 }}
            className="text-xl font-semibold text-foreground tracking-tight"
          >
            Calculating Tax Metrics
          </motion.h3>

          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.4 }}
            className="text-sm text-foreground-500 leading-relaxed max-w-xs mx-auto"
          >
            Processing trade data and computing performance metrics for comprehensive tax analysis
          </motion.p>

          {/* Animated progress indicators */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.4 }}
            className="flex items-center justify-center gap-1.5 pt-2"
          >
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-primary rounded-full"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.4, 1, 0.4],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                  ease: "easeInOut"
                }}
              />
            ))}
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
});

TaxMetricsLoader.displayName = 'TaxMetricsLoader';

// PERFORMANCE: Memoized metrics calculation component
const TaxMetrics = memo<{
  maxCummPF: number;
  minCummPF: number;
  currentDrawdown: number;
  totalGrossPL: number;
  totalTaxes: number;
  totalNetPL: number;
  tradesFullyCalculated: boolean;
  onDrawdownClick: () => void;
}>(({
  maxCummPF,
  minCummPF,
  currentDrawdown,
  totalGrossPL,
  totalTaxes,
  totalNetPL,
  tradesFullyCalculated,
  onDrawdownClick
}) => {
  const formatCurrency = useCallback((value: number) =>
    new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value), []);

  // PERFORMANCE: Static tooltip content to prevent re-renders
  const tooltipContent = useMemo(() => ({
    maxCummPF: (
      <div className="max-w-xs p-2 space-y-2 text-sm">
        <p className="font-medium text-default-600">Maximum Cumulative PF (Peak)</p>
        <p>The highest point your cumulative profit factor ever reached during this period. This represents your portfolio's best performance peak, which may be higher than your current PF if you've experienced drawdowns since then.</p>
      </div>
    ),
    minCummPF: (
      <div className="max-w-xs p-2 space-y-2 text-sm">
        <p className="font-medium text-default-600">Minimum Cumulative PF (Lowest)</p>
        <p>The lowest point your cumulative profit factor reached during this period. This shows your worst drawdown level.</p>
      </div>
    )
  }), []);

  if (!tradesFullyCalculated) {
    return (
      <TaxMetricsLoader />
    );
  }

  // CRITICAL FIX: Show "No data available" when no trades exist - check for actual absence of data
  // Only show "No Data Available" if we have no cumulative PF values (indicating no closed trades)
  if (maxCummPF === 0 && minCummPF === 0 && totalGrossPL === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 px-4 text-center">
        <Icon icon="lucide:bar-chart-3" className="w-12 h-12 text-default-300 mb-3" />
        <h3 className="text-lg font-medium text-default-600 mb-2">No Data Available</h3>
        <p className="text-sm text-default-400 max-w-xs">
          No closed trades found for the selected period. Start trading to see your tax metrics here.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <span className="text-default-600">Max Cumm PF (Peak)</span>
          <Tooltip content={tooltipContent.maxCummPF} placement="right" showArrow>
            <Button isIconOnly size="sm" variant="light" className="min-w-unit-5 w-unit-5 h-unit-5 text-default-400">
              <Icon icon="lucide:info" className="w-3 h-3" />
            </Button>
          </Tooltip>
        </div>
        <span className="text-[#00B386] font-medium">{maxCummPF.toFixed(2)}%</span>
      </div>

      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <span className="text-default-600">Min Cumm PF</span>
          <Tooltip content={tooltipContent.minCummPF} placement="right" showArrow>
            <Button isIconOnly size="sm" variant="light" className="min-w-unit-5 w-unit-5 h-unit-5 text-default-400">
              <Icon icon="lucide:info" className="w-3 h-3" />
            </Button>
          </Tooltip>
        </div>
        <span className="text-[#FF3B3B] font-medium">{minCummPF.toFixed(2)}%</span>
      </div>

      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <span className="text-default-600">Drawdown</span>
          <Button
            isIconOnly size="sm" variant="light"
            className="min-w-unit-5 w-unit-5 h-unit-5 text-default-400 hover:text-primary transition-colors"
            onPress={onDrawdownClick}
          >
            <Icon icon="lucide:table" className="w-3 h-3" />
          </Button>
        </div>
        {currentDrawdown < 0.001 ? (
          <span className="text-[#00B386] font-medium flex items-center gap-1">
            <Icon icon="lucide:rocket" className="w-4 h-4" />
            Hurray! Flying high
          </span>
        ) : (
          <span className="text-[#FF3B3B] font-medium text-sm">
            {currentDrawdown.toFixed(2)}% OF PF LOST FROM TOP
          </span>
        )}
      </div>

      <Divider className="my-4" />

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <span className="text-default-600">Total Gross P/L</span>
          <span className={`font-medium ${totalGrossPL >= 0 ? 'text-[#00B386]' : 'text-[#FF3B3B]'}`}>
            {formatCurrency(totalGrossPL)}
          </span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-default-600">Total Taxes</span>
          <span className="text-[#FF3B3B] font-medium">{formatCurrency(totalTaxes)}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-default-600">Total Net P/L</span>
          <span className={`font-medium ${totalNetPL >= 0 ? 'text-[#00B386]' : 'text-[#FF3B3B]'}`}>
            {formatCurrency(totalNetPL)}
          </span>
        </div>
      </div>
    </div>
  );
});

// PERFORMANCE: Main component with optimized state management
export const TaxAnalytics: React.FC = () => {
  const { trades } = useTrades();
  const { accountingMethod } = useAccountingMethod();
  const { filter } = useGlobalFilter();
  const useCashBasis = accountingMethod === 'cash';

  // PERFORMANCE: Memoize expensive calculations
  const tradeYears = useMemo(() =>
    Array.from(new Set(trades.map(t => new Date(t.date).getFullYear()))).sort((a, b) => b - a),
    [trades]
  );

  const currentYear = new Date().getFullYear();
  const yearOptions = useMemo(() => ['All time', ...tradeYears.map(String)], [tradeYears]);
  const defaultYear = currentYear.toString();

  // State management
  const [selectedYear, setSelectedYear] = React.useState(defaultYear);
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [selectedMonth, setSelectedMonth] = React.useState<string | null>(null);
  const [isDrawdownModalOpen, setIsDrawdownModalOpen] = React.useState(false);
  const [taxesByMonth, setTaxesByMonth] = React.useState<{ [month: string]: number }>({});

  const monthOrder = useMemo(() =>
    ["January","February","March","April","May","June","July","August","September","October","November","December"],
    []
  );

  // PERFORMANCE: Optimized tax data loading with caching
  const loadTaxData = useCallback(async () => {
    try {
      const allTaxData = await getCachedTaxData();

      if (selectedYear === 'All time') {
        const allYearData: { [month: string]: number } = {};
        monthOrder.forEach(month => { allYearData[month] = 0; });

        Object.entries(allTaxData).forEach(([year, yearData]: [string, any]) => {
          if (typeof yearData === 'object' && yearData !== null) {
            Object.entries(yearData).forEach(([month, amount]) => {
              if (typeof amount === 'number') {
                allYearData[month] = (allYearData[month] || 0) + amount;
              }
            });
          }
        });
        setTaxesByMonth(allYearData);
      } else {
        const yearData = allTaxData[selectedYear] || {};
        const completeYearData: { [month: string]: number } = {};
        monthOrder.forEach(month => {
          completeYearData[month] = yearData[month] || 0;
        });
        setTaxesByMonth(completeYearData);
      }
    } catch (error) {
      // Silent error handling - no console.log for performance
      setTaxesByMonth({});
    }
  }, [selectedYear, monthOrder]);

  // PERFORMANCE: Optimized effect with proper dependencies
  React.useEffect(() => {
    loadTaxData();
  }, [loadTaxData]);

  // CRITICAL FIX: Setup tax data listener for cache invalidation
  React.useEffect(() => {
    const cleanup = setupTaxDataListener();

    // Also listen for year changes to reload data
    const handleTaxDataUpdate = () => {
      console.log('🔄 Tax data updated, reloading...');
      loadTaxData();
    };

    window.addEventListener('taxDataUpdated', handleTaxDataUpdate);

    return () => {
      cleanup();
      window.removeEventListener('taxDataUpdated', handleTaxDataUpdate);
    };
  }, [loadTaxData]);

  // PERFORMANCE: Initialize months efficiently
  React.useEffect(() => {
    const initial: { [month: string]: number } = {};
    let needsUpdate = false;

    monthOrder.forEach(month => {
      if (!(month in taxesByMonth)) {
        initial[month] = 0;
        needsUpdate = true;
      }
    });

    if (needsUpdate) {
      setTaxesByMonth(prev => ({ ...initial, ...prev }));
    }
  }, [monthOrder, taxesByMonth]);

  // PERFORMANCE: Optimized trade calculation checks
  const tradesFullyCalculated = useMemo(() => {
    // CRITICAL FIX: Handle empty trades or all zero PF cases
    if (trades.length === 0) {
      return true; // No trades = fully calculated (empty state)
    }

    const closedTrades = trades.filter(t =>
      t.positionStatus === 'Closed' || t.positionStatus === 'Partial'
    );

    // If no closed trades, consider it calculated (empty state)
    if (closedTrades.length === 0) {
      return true;
    }

    // Check if closed trades have cummPf calculated (can be 0)
    const tradesWithCummPf = closedTrades.filter(t =>
      typeof t.cummPf === 'number' && !isNaN(t.cummPf)
    );

    // Consider fully calculated if all closed trades have cummPf (even if 0)
    return tradesWithCummPf.length === closedTrades.length;
  }, [trades]);

  // PERFORMANCE: Stable hash for dependency tracking
  const tradesHash = useMemo(() => {
    return trades.map(t => `${t.id}-${t.cummPf?.toFixed(6) || 0}-${t.positionStatus}`).join('|');
  }, [trades]);

  // PERFORMANCE: Memoized trade filtering and processing
  const { tradesForYear, closedTrades, cummPfs } = useMemo(() => {
    if (!tradesFullyCalculated) {
      return { tradesForYear: [], closedTrades: [], cummPfs: [] };
    }

    let filteredTrades = selectedYear === 'All time' ? trades : trades.filter(t => t.date.startsWith(selectedYear));

    if (useCashBasis) {
      const seenTradeIds = new Set();
      filteredTrades = filteredTrades.filter(trade => {
        const originalId = trade.id.split('_exit_')[0];
        if (seenTradeIds.has(originalId)) return false;
        seenTradeIds.add(originalId);
        return true;
      });
    }

    const closedTradesFiltered = filteredTrades
      .filter(t => t.positionStatus === "Closed" || t.positionStatus === "Partial")
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    const cummPfs = closedTradesFiltered
      .map(t => t.cummPf)
      .filter(v => typeof v === 'number' && !isNaN(v));

    return {
      tradesForYear: filteredTrades,
      closedTrades: closedTradesFiltered,
      cummPfs
    };
  }, [tradesHash, selectedYear, useCashBasis, tradesFullyCalculated]);


  // CRITICAL FIX: Corrected drawdown breakdown calculation with proper logic
  const drawdownBreakdown = useMemo(() => {
    if (closedTrades.length === 0) return [];

    // CRITICAL FIX: Initialize runningMax to 0 to handle negative starting values
    let runningMax = 0;
    let previousPF = 0;
    let wasInDrawdown = false;

    return closedTrades.map((trade, index) => {
      const currentPF = trade.cummPf || 0;
      const stockPFImpact = trade.pfImpact || 0;

      // CRITICAL FIX: Handle first trade initialization
      if (index === 0) {
        runningMax = Math.max(0, currentPF);
      }

      // CRITICAL FIX: Proper peak detection
      const isNewPeak = currentPF > runningMax;

      // Update running max AFTER checking if it's a new peak
      if (isNewPeak) {
        runningMax = currentPF;
        wasInDrawdown = false;
      }

      // CRITICAL FIX: Correct drawdown calculation - always calculate from peak
      const drawdownFromPeak = runningMax - currentPF;

      // Track if we're currently in drawdown
      const isInDrawdown = drawdownFromPeak > 0;
      const isRecovery = wasInDrawdown && drawdownFromPeak === 0 && !isNewPeak;

      // CRITICAL FIX: Corrected commentary logic
      let commentary = "No commentary";
      let commentaryType = "neutral";

      if (index === 0) {
        commentary = "Portfolio inception";
        commentaryType = "start";
      } else if (isNewPeak) {
        commentary = "New peak achieved";
        commentaryType = "peak";
      } else if (isRecovery) {
        commentary = "Full recovery achieved";
        commentaryType = "recovery";
      } else if (drawdownFromPeak === 0) {
        commentary = "At peak level";
        commentaryType = "neutral";
      } else if (drawdownFromPeak > 0 && drawdownFromPeak <= 2) {
        commentary = "Minor correction";
        commentaryType = "mild";
      } else if (drawdownFromPeak > 2 && drawdownFromPeak <= 5) {
        commentary = "Moderate drawdown";
        commentaryType = "moderate";
      } else if (drawdownFromPeak > 5 && drawdownFromPeak <= 10) {
        commentary = "Significant drawdown";
        commentaryType = "moderate";
      } else if (drawdownFromPeak > 10) {
        commentary = "Deep drawdown";
        commentaryType = "severe";
      }

      // Update state for next iteration
      if (isInDrawdown) {
        wasInDrawdown = true;
      }

      const displayDate = useCashBasis ?
        (trade.exit1Date || trade.exit2Date || trade.exit3Date || trade.date) :
        trade.date;

      previousPF = currentPF;

      // CRITICAL FIX: Create unique key to prevent React warnings
      const uniqueKey = `${index}-${trade.id}-${displayDate}-${trade.name || 'Unknown'}`;

      return {
        uniqueKey,
        date: displayDate,
        symbol: trade.name || 'Unknown',
        stockPFImpact,
        cummPFImpact: currentPF,
        drawdownFromPeak,
        isNewPeak,
        commentary,
        commentaryType
      };
    });
  }, [closedTrades, useCashBasis]);

  // PERFORMANCE: Optimized metrics calculation with stable hash
  const cummPfsHash = useMemo(() => {
    return cummPfs.map(v => v.toFixed(6)).join('|');
  }, [cummPfs]);

  const prevValuesRef = useRef({ maxCummPF: 0, minCummPF: 0, cummPfsHash: '' });

  // PERFORMANCE: Optimized metrics calculation without excessive logging
  const { maxCummPF, minCummPF, maxDrawdown, currentDrawdown, currentCummPF } = useMemo(() => {
    if (cummPfs.length === 0) {
      return {
        maxCummPF: 0,
        minCummPF: 0,
        maxDrawdown: 0,
        currentDrawdown: 0,
        currentCummPF: 0
      };
    }

    const maxPF = Math.max(...cummPfs);
    const currentPF = cummPfs[cummPfs.length - 1];
    const minPF = Math.min(...cummPfs);

    let maxDrawdownPoints = 0;

    // CRITICAL FIX: For all negative values, Max DD = absolute value of worst (most negative) performance
    if (maxPF <= 0) {
      // All values are negative - Max DD is the absolute value of the worst performance
      maxDrawdownPoints = Math.abs(minPF);
    } else {
      // Standard max drawdown calculation for positive peaks
      let runningMax = cummPfs[0];
      cummPfs.forEach(pf => {
        if (pf > runningMax) runningMax = pf;
        const ddPoints = runningMax - pf;
        if (ddPoints > maxDrawdownPoints) maxDrawdownPoints = ddPoints;
      });
    }

    // CRITICAL FIX: Match drawdown breakdown table logic exactly
    // When all values are negative, drawdown from peak = absolute value of current PF
    let currentDrawdownFromPeak = 0;
    if (maxPF <= 0) {
      // All negative performance - show absolute current loss as drawdown
      currentDrawdownFromPeak = Math.abs(currentPF);
    } else {
      // Standard drawdown calculation for positive peaks
      currentDrawdownFromPeak = Math.max(0, maxPF - currentPF);
    }

    prevValuesRef.current = {
      maxCummPF: maxPF,
      minCummPF: minPF,
      cummPfsHash: cummPfsHash
    };

    return {
      maxCummPF: maxPF,
      minCummPF: minPF,
      maxDrawdown: maxDrawdownPoints,
      currentDrawdown: currentDrawdownFromPeak,
      currentCummPF: currentPF
    };
  }, [cummPfsHash]);
  // PERFORMANCE: Memoized P/L calculations
  const { totalGrossPL, totalTaxes, totalNetPL } = useMemo(() => {
    let grossPL = 0;

    if (useCashBasis) {
      const allTradesForYear = selectedYear === 'All time' ? trades : trades.filter(t => t.date.startsWith(selectedYear));
      const expandedTrades = allTradesForYear.flatMap(trade =>
        Array.isArray(trade._expandedTrades)
          ? trade._expandedTrades.filter(t => t._cashBasisExit)
          : (trade._cashBasisExit ? [trade] : [])
      );
      grossPL = expandedTrades.reduce((sum, t) => sum + calculateTradePL(t, useCashBasis), 0);
    } else {
      grossPL = tradesForYear.reduce((sum, t) => sum + calculateTradePL(t, useCashBasis), 0);
    }

    const taxes = monthOrder.reduce((sum, m) => sum + (taxesByMonth[m] || 0), 0);
    const netPL = grossPL - taxes;

    return { totalGrossPL: grossPL, totalTaxes: taxes, totalNetPL: netPL };
  }, [useCashBasis, selectedYear, trades, tradesForYear, monthOrder, taxesByMonth]);

  // PERFORMANCE: Memoized formatters
  const formatCurrency = useCallback((value: number) =>
    new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value), []);

  const formatPercent = useCallback((value: number) => value.toFixed(2) + "%", []);

  // PERFORMANCE: Optimized year change handler with debouncing
  const handleYearChange = useCallback((keys: any) => {
    const selected = Array.from(keys)[0] as string;
    console.log('🔄 Tax Analytics Year Change:', { selected, currentSelectedYear: selectedYear, keys: Array.from(keys) });

    // PERFORMANCE FIX: Only update if actually changed
    if (selected !== selectedYear) {
      console.log('✅ Tax Analytics Year Updated:', { from: selectedYear, to: selected });
      setSelectedYear(selected);

      // PERFORMANCE FIX: Debounce cache invalidation to prevent excessive calls
      setTimeout(() => {
        invalidateTaxDataCache();
      }, 100);
    }
  }, [selectedYear]);

  const handleDrawdownClick = useCallback(() => {
    setIsDrawdownModalOpen(true);
  }, []);

  return (
    <div className="space-y-6">
      <motion.div
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center gap-3">
          <Dropdown>
            <DropdownTrigger>
              <Button
                variant="light"
                endContent={<Icon icon="lucide:chevron-down" className="text-sm" />}
                size="sm"
                radius="full"
                className="font-medium text-xs h-7 px-3"
                disableRipple
              >
                {selectedYear}
              </Button>
            </DropdownTrigger>
            <DropdownMenu
              aria-label="Year selection"
              selectionMode="single"
              selectedKeys={new Set([selectedYear])}
              onSelectionChange={handleYearChange}
              disallowEmptySelection
              closeOnSelect
            >
              {yearOptions.map((option) => (
                <DropdownItem key={option} textValue={option}>
                  {option}
                </DropdownItem>
              ))}
            </DropdownMenu>
          </Dropdown>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="light"
            startContent={<Icon icon="lucide:download" className="w-3.5 h-3.5" />}
            size="sm"
            radius="full"
            className="font-medium text-xs h-7 px-3"
          >
            Export
          </Button>
        </div>
      </motion.div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <h3 className="text-xl font-semibold tracking-tight">Tax Summary</h3>
          </CardHeader>
          <Divider />
          <CardBody>
            <TaxSummaryChart taxesByMonth={taxesByMonth} selectedYear={selectedYear} />
          </CardBody>
        </Card>
        <Card>
          <CardHeader>
            <h3 className="text-xl font-semibold tracking-tight">Tax Metrics</h3>
          </CardHeader>
          <Divider />
          <CardBody className="p-6 space-y-8">
            <TaxMetrics
              maxCummPF={maxCummPF}
              minCummPF={minCummPF}
              currentDrawdown={currentDrawdown}
              totalGrossPL={totalGrossPL}
              totalTaxes={totalTaxes}
              totalNetPL={totalNetPL}
              tradesFullyCalculated={tradesFullyCalculated}
              onDrawdownClick={handleDrawdownClick}
            />
          </CardBody>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <h3 className="text-xl font-semibold tracking-tight">Monthly Tax Breakdown</h3>
        </CardHeader>
        <Divider />
        <CardBody>
          <TaxTable
            trades={trades}
            taxesByMonth={taxesByMonth}
            setTaxesByMonth={setTaxesByMonth}
            selectedYear={selectedYear}
          />
        </CardBody>
      </Card>

      <TaxEditModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        month={selectedMonth}
      />

      {/* PERFORMANCE: Optimized Drawdown Modal */}
      <Modal
        isOpen={isDrawdownModalOpen}
        onOpenChange={setIsDrawdownModalOpen}
        size="3xl"
        scrollBehavior="inside"
        classNames={{
          base: "transform-gpu backdrop-blur-sm",
          wrapper: "transform-gpu",
          backdrop: "bg-black/40",
          closeButton: "text-foreground/60 hover:bg-white/10"
        }}
        backdrop="blur"
      >
        <ModalContent className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-2xl border border-gray-200 dark:border-gray-700 shadow-2xl max-h-[85vh]">
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1 border-b border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-900/80 px-4 py-3">
                <div className="flex items-center gap-2">
                  <div className="p-1.5 rounded-lg bg-primary/10">
                    <Icon icon="lucide:trending-down" className="text-primary text-sm" />
                  </div>
                  <div>
                    <span className="text-base font-semibold">Drawdown Breakdown</span>
                    <p className="text-xs text-default-500 mt-0.5">
                      {useCashBasis ? 'Cash Basis' : 'Accrual Basis'} • {selectedYear}
                    </p>
                  </div>
                </div>
              </ModalHeader>
              <ModalBody className="p-4">
                <div className="space-y-3">
                  <div className="p-2 bg-content1/20 rounded-lg border border-divider/20">
                    <div className="flex items-center justify-between">
                      <p className="text-xs font-medium text-foreground">
                        {drawdownBreakdown.length} trades • Max DD: <span className="text-danger">{maxDrawdown.toFixed(2)}%</span>
                      </p>
                      <p className="text-xs text-default-500">
                        {useCashBasis ? 'Exit dates' : 'Entry dates'}
                      </p>
                    </div>
                  </div>

                  <div className="max-h-[55vh] border border-divider/30 rounded-lg overflow-auto">
                    <Table
                      aria-label="Drawdown breakdown table"
                      classNames={{
                        wrapper: "shadow-none border-none",
                        table: "border-collapse table-fixed w-full min-w-[720px]",
                        th: "bg-background text-sm font-medium text-default-600 border-b border-divider/30 px-3 py-2.5 sticky top-0 z-10",
                        td: "py-2.5 px-3 text-sm border-b border-divider/20",
                        tr: "hover:bg-content1/20 transition-colors"
                      }}
                      removeWrapper={true}
                    >
                      <TableHeader>
                        <TableColumn key="date" align="start" width={90}>Date</TableColumn>
                        <TableColumn key="symbol" align="start" width={120}>Symbol</TableColumn>
                        <TableColumn key="stockPF" align="center" width={120}>Stock PF Impact</TableColumn>
                        <TableColumn key="cummPF" align="center" width={120}>Cum PF Impact</TableColumn>
                        <TableColumn key="drawdown" align="center" width={120}>DD From Peak</TableColumn>
                        <TableColumn key="commentary" align="start" width={150}>Commentary</TableColumn>
                      </TableHeader>
                      <TableBody items={drawdownBreakdown.filter(item => item && item.symbol)}>
                        {(item) => (
                          <TableRow
                            key={item.uniqueKey}
                            className={`${item.isNewPeak ? "bg-success/10 border-l-4 border-l-success" : ""} transition-colors`}
                          >
                            <TableCell>
                              <div className="flex items-center gap-1">
                                {item.isNewPeak && (
                                  <Icon icon="lucide:crown" className="w-3 h-3 text-warning" />
                                )}
                                <span className="text-sm">
                                  {new Date(item.date).toLocaleDateString('en-GB', {
                                    day: '2-digit',
                                    month: '2-digit',
                                    year: 'numeric'
                                  })}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="font-medium text-sm">
                              <span className="block" title={item.symbol}>
                                {item.symbol}
                              </span>
                            </TableCell>
                            <TableCell className="text-center">
                              <span className={`text-sm font-medium ${item.stockPFImpact >= 0 ? "text-success" : "text-danger"}`}>
                                {item.stockPFImpact >= 0 ? "+" : ""}{item.stockPFImpact.toFixed(2)}%
                              </span>
                            </TableCell>
                            <TableCell className="text-center">
                              <span className="text-sm font-medium">
                                {item.cummPFImpact.toFixed(2)}%
                              </span>
                            </TableCell>
                            <TableCell className="text-center">
                              <span className={`text-sm font-medium ${item.drawdownFromPeak > 0 ? "text-danger" : "text-success"}`}>
                                {item.drawdownFromPeak === 0 ? "0.00%" : `-${item.drawdownFromPeak.toFixed(2)}%`}
                              </span>
                            </TableCell>
                            <TableCell>
                              <div
                                className={`text-xs px-2 py-1.5 rounded-md font-medium leading-tight ${
                                  item.commentaryType === 'peak' ? 'bg-success/10 text-success' :
                                  item.commentaryType === 'recovery' ? 'bg-primary/10 text-primary' :
                                  item.commentaryType === 'mild' ? 'bg-warning/10 text-warning' :
                                  item.commentaryType === 'moderate' ? 'bg-danger/10 text-danger' :
                                  item.commentaryType === 'severe' ? 'bg-danger/20 text-danger' :
                                  'bg-default/10 text-default-600'
                                }`}
                              >
                                <div className="max-w-[200px] break-words whitespace-normal">
                                  {item.commentary}
                                </div>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </ModalBody>
              <ModalFooter className="border-t border-gray-200 dark:border-gray-700 px-4 py-1.5">
                <Button
                  variant="flat"
                  onPress={onClose}
                  size="sm"
                  className="w-auto px-4 py-1 text-xs h-7"
                  startContent={<Icon icon="lucide:x" className="w-3 h-3" />}
                >
                  Close
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

// PERFORMANCE: Clear cache on component unmount to prevent memory leaks
TaxAnalytics.displayName = 'TaxAnalytics';

export default TaxAnalytics;