import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { SupabaseService } from '../services/supabaseService';

export type AccountingMethod = 'accrual' | 'cash';

interface AccountingMethodContextType {
  accountingMethod: AccountingMethod;
  setAccountingMethod: (method: AccountingMethod) => void;
  toggleAccountingMethod: () => void;
  clearAccountingMethodData: () => void;
}

const AccountingMethodContext = createContext<AccountingMethodContextType | undefined>(undefined);

interface AccountingMethodProviderProps {
  children: ReactNode;
}

export const AccountingMethodProvider: React.FC<AccountingMethodProviderProps> = ({ children }) => {
  const [accountingMethod, setAccountingMethodState] = useState<AccountingMethod>('cash');
  const [isLoading, setIsLoading] = useState(true);

  // Load accounting method from Supabase on mount
  useEffect(() => {
    const loadAccountingMethod = async () => {
      try {
        const stored = await SupabaseService.getMiscData('accountingMethod');
        if (stored && (stored === 'accrual' || stored === 'cash')) {
          setAccountingMethodState(stored as AccountingMethod);
        } else {
          // If no stored preference, default to cash basis and save it
          setAccountingMethodState('cash');
          await SupabaseService.saveMiscData('accountingMethod', 'cash');
        }
      } catch (error) {
        // Even if Supabase fails, ensure we default to cash basis
        setAccountingMethodState('cash');
      } finally {
        setIsLoading(false);
      }
    };

    loadAccountingMethod();
  }, []);

  // Memoized setter to prevent unnecessary re-renders
  const setAccountingMethod = React.useCallback((method: AccountingMethod) => {
    if (method === accountingMethod) return; // Prevent unnecessary updates

    // Immediate state update for responsive UI
    setAccountingMethodState(method);

    // Async Supabase update to prevent blocking
    requestIdleCallback(() => {
      SupabaseService.saveMiscData('accountingMethod', method).catch(error => {
        // Handle error silently
      });
    });
  }, [accountingMethod]);

  const toggleAccountingMethod = React.useCallback(() => {
    const newMethod = accountingMethod === 'accrual' ? 'cash' : 'accrual';
    setAccountingMethod(newMethod);
  }, [accountingMethod, setAccountingMethod]);

  const clearAccountingMethodData = React.useCallback(() => {
    try {
      localStorage.removeItem('accountingMethod');
      setAccountingMethodState('cash'); // Reset to default
      } catch (error) {
      }
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = React.useMemo(() => ({
    accountingMethod,
    setAccountingMethod,
    toggleAccountingMethod,
    clearAccountingMethodData
  }), [accountingMethod, setAccountingMethod, toggleAccountingMethod, clearAccountingMethodData]);

  // Always render children to prevent hook count mismatches
  return (
    <AccountingMethodContext.Provider value={contextValue}>
      {children}
    </AccountingMethodContext.Provider>
  );
};

export const useAccountingMethod = (): AccountingMethodContextType => {
  const context = useContext(AccountingMethodContext);
  if (!context) {
    throw new Error('useAccountingMethod must be used within an AccountingMethodProvider');
  }
  return context;
};
