import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback, useMemo, useRef } from "react";
import { getExitDatesWithFallback } from './accountingUtils';
import { SupabaseService } from '../services/supabaseService';
import { v4 as uuidv4 } from 'uuid';

export interface YearlyStartingCapital {
  id: string;
  year: number;
  startingCapital: number;
  updatedAt: string;
}

export interface MonthlyStartingCapitalOverride {
  id: string;
  month: string; // Short month name like 'Jan', 'Feb'
  year: number;
  startingCapital: number;
  updatedAt: string;
}

export interface CapitalChange {
  id: string;
  date: string;
  amount: number;  // Positive for deposits, negative for withdrawals
  type: 'deposit' | 'withdrawal';
  description: string;
}

export interface MonthlyTruePortfolio {
  month: string;
  year: number;
  startingCapital: number;
  capitalChanges: number; // Net deposits - withdrawals for the month
  pl: number; // P&L from trades for the month
  finalCapital: number; // Starting + changes + P&L
}

interface TruePortfolioContextType {
  // Core functions
  getTruePortfolioSize: (month: string, year: number, trades?: any[], useCashBasis?: boolean) => number;
  getLatestTruePortfolioSize: (trades?: any[], useCashBasis?: boolean) => number;

  // Starting capital management
  yearlyStartingCapitals: YearlyStartingCapital[];
  setYearlyStartingCapital: (year: number, amount: number) => void;
  getYearlyStartingCapital: (year: number) => number;

  // Monthly starting capital overrides
  monthlyStartingCapitalOverrides: MonthlyStartingCapitalOverride[];
  setMonthlyStartingCapitalOverride: (month: string, year: number, amount: number) => void;
  removeMonthlyStartingCapitalOverride: (month: string, year: number) => void;
  getMonthlyStartingCapitalOverride: (month: string, year: number) => number | null;

  // Capital changes
  capitalChanges: CapitalChange[];
  addCapitalChange: (change: Omit<CapitalChange, 'id'>) => void;
  updateCapitalChange: (change: CapitalChange) => void;
  deleteCapitalChange: (id: string) => void;

  // Monthly calculations
  getMonthlyTruePortfolio: (month: string, year: number, trades?: any[], useCashBasis?: boolean) => MonthlyTruePortfolio;
  getAllMonthlyTruePortfolios: (trades?: any[], useCashBasis?: boolean) => MonthlyTruePortfolio[];

  // Cleanup tools
  cleanupDuplicates: () => Promise<{
    yearlyCapitals: { before: number; after: number };
    capitalChanges: { before: number; after: number };
    monthlyOverrides: { before: number; after: number };
  }>;

  // Backward compatibility
  portfolioSize: number; // Latest true portfolio size
}

const TruePortfolioContext = createContext<TruePortfolioContextType | undefined>(undefined);

// Supabase helpers - now using portfolio_data table
async function fetchYearlyStartingCapitals(): Promise<YearlyStartingCapital[]> {
  try {
    const data = await SupabaseService.getYearlyStartingCapitals();
    // Convert from portfolio_data format to YearlyStartingCapital format
    const capitals = data.map(item => ({
      id: item.id,
      year: item.year,
      startingCapital: item.amount,
      updatedAt: item.updated_at
    }));
    return capitals;
  } catch (error) {
    return [];
  }
}

async function saveYearlyStartingCapitals(capitals: YearlyStartingCapital[]) {
  try {
    await SupabaseService.saveYearlyStartingCapitals(capitals);
  } catch (error) {
    // Handle error silently
  }
}

async function fetchCapitalChanges(): Promise<CapitalChange[]> {
  try {
    const data = await SupabaseService.getCapitalChanges();
    // Convert from portfolio_data format to CapitalChange format
    const changes = data.map(item => ({
      id: item.id,
      date: item.date,
      amount: item.amount,
      // CRITICAL FIX: Determine type based on amount sign for consistency
      type: item.amount >= 0 ? 'deposit' : 'withdrawal',
      description: item.description || ''
    }));
    return changes;
  } catch (error) {
    return [];
  }
}

async function saveCapitalChanges(changes: CapitalChange[]) {
  try {
    await SupabaseService.saveCapitalChanges(changes);
  } catch (error) {
    // Handle error silently
  }
}

async function fetchMonthlyStartingCapitalOverrides(): Promise<MonthlyStartingCapitalOverride[]> {
  try {
    const data = await SupabaseService.getMonthlyStartingCapitalOverrides();
    // Convert from portfolio_data format to MonthlyStartingCapitalOverride format
    const overrides = data.map(item => ({
      id: item.id,
      month: item.month,
      year: item.year,
      startingCapital: item.amount,
      updatedAt: item.updated_at
    }));
    return overrides;
  } catch (error) {
    return [];
  }
}

async function saveMonthlyStartingCapitalOverrides(overrides: MonthlyStartingCapitalOverride[]) {
  try {
    await SupabaseService.saveMonthlyStartingCapitalOverrides(overrides);
  } catch (error) {
    // Handle error silently
  }
}

export const TruePortfolioProvider = ({ children }: { children: ReactNode }) => {
  const [yearlyStartingCapitals, setYearlyStartingCapitals] = useState<YearlyStartingCapital[]>([]);
  const [capitalChanges, setCapitalChanges] = useState<CapitalChange[]>([]);
  const [monthlyStartingCapitalOverrides, setMonthlyStartingCapitalOverrides] = useState<MonthlyStartingCapitalOverride[]>([]);
  const [hydrated, setHydrated] = useState(false);
  const isLoadingRef = useRef(false);

  // Load from Supabase on mount
  useEffect(() => {
    const loadData = async () => {
      if (isLoadingRef.current) {
        return; // Prevent multiple simultaneous loads
      }

      isLoadingRef.current = true;

      try {
        // Load from Supabase
        const [capitals, changes, overrides] = await Promise.all([
          fetchYearlyStartingCapitals(),
          fetchCapitalChanges(),
          fetchMonthlyStartingCapitalOverrides()
        ]);

        if (Array.isArray(capitals)) {
          setYearlyStartingCapitals(capitals);
        }

        if (Array.isArray(changes)) {
          setCapitalChanges(changes);
        }

        if (Array.isArray(overrides)) {
          setMonthlyStartingCapitalOverrides(overrides);
        }
      } catch (error) {
        // Handle error silently
      } finally {
        setHydrated(true);
        isLoadingRef.current = false;
      }
    };

    loadData();
  }, []);

  // CRITICAL FIX: Add debounced save to prevent duplicate entries
  const saveTimeoutRef = useRef<{
    yearlyCapitals?: NodeJS.Timeout;
    capitalChanges?: NodeJS.Timeout;
    monthlyOverrides?: NodeJS.Timeout;
  }>({});

  // Save to Supabase when data changes (with debouncing)
  useEffect(() => {
    if (hydrated && yearlyStartingCapitals.length > 0) {
      // Clear existing timeout
      if (saveTimeoutRef.current.yearlyCapitals) {
        clearTimeout(saveTimeoutRef.current.yearlyCapitals);
      }

      // Set new timeout
      saveTimeoutRef.current.yearlyCapitals = setTimeout(() => {
        saveYearlyStartingCapitals(yearlyStartingCapitals);
      }, 500); // 500ms debounce
    }
  }, [yearlyStartingCapitals, hydrated]);

  useEffect(() => {
    if (hydrated && capitalChanges.length >= 0) { // Allow empty arrays to clear data
      // Clear existing timeout
      if (saveTimeoutRef.current.capitalChanges) {
        clearTimeout(saveTimeoutRef.current.capitalChanges);
      }

      // Set new timeout
      saveTimeoutRef.current.capitalChanges = setTimeout(() => {
        saveCapitalChanges(capitalChanges);
      }, 500); // 500ms debounce
    }
  }, [capitalChanges, hydrated]);

  useEffect(() => {
    if (hydrated && monthlyStartingCapitalOverrides.length >= 0) { // Allow empty arrays to clear data
      // Clear existing timeout
      if (saveTimeoutRef.current.monthlyOverrides) {
        clearTimeout(saveTimeoutRef.current.monthlyOverrides);
      }

      // Set new timeout
      saveTimeoutRef.current.monthlyOverrides = setTimeout(() => {
        saveMonthlyStartingCapitalOverrides(monthlyStartingCapitalOverrides);
      }, 500); // 500ms debounce
    }
  }, [monthlyStartingCapitalOverrides, hydrated]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      Object.values(saveTimeoutRef.current).forEach(timeout => {
        if (timeout) clearTimeout(timeout);
      });
    };
  }, []);

  const setYearlyStartingCapital = useCallback((year: number, amount: number) => {
    setYearlyStartingCapitals(prev => {
      const updated = [...prev];
      const existingIndex = updated.findIndex(item => item.year === year);

      const newCapital: YearlyStartingCapital = {
        // CRITICAL FIX: Use existing ID if updating, otherwise generate new UUID
        id: existingIndex >= 0 ? updated[existingIndex].id : uuidv4(),
        year,
        startingCapital: amount,
        updatedAt: new Date().toISOString()
      };

      if (existingIndex >= 0) {
        updated[existingIndex] = newCapital;
      } else {
        updated.push(newCapital);
      }

      return updated.sort((a, b) => a.year - b.year);
    });
  }, []);

  const getYearlyStartingCapital = useCallback((year: number): number => {
    const capital = yearlyStartingCapitals.find(item => item.year === year);
    return capital?.startingCapital || 0;
  }, [yearlyStartingCapitals]);

  const setMonthlyStartingCapitalOverride = useCallback((month: string, year: number, amount: number) => {
    const normalizedMonth = month.length > 3 ?
      ({ "January": "Jan", "February": "Feb", "March": "Mar", "April": "Apr", "May": "May", "June": "Jun",
         "July": "Jul", "August": "Aug", "September": "Sep", "October": "Oct", "November": "Nov", "December": "Dec" }[month] || month) :
      month;

    setMonthlyStartingCapitalOverrides(prev => {
      const updated = [...prev];
      const existingIndex = updated.findIndex(item => item.month === normalizedMonth && item.year === year);

      const newOverride: MonthlyStartingCapitalOverride = {
        // CRITICAL FIX: Use existing ID if updating, otherwise generate new UUID
        id: existingIndex >= 0 ? updated[existingIndex].id : uuidv4(),
        month: normalizedMonth,
        year,
        startingCapital: amount,
        updatedAt: new Date().toISOString()
      };

      if (existingIndex >= 0) {
        updated[existingIndex] = newOverride;
      } else {
        updated.push(newOverride);
      }

      return updated.sort((a, b) => a.year - b.year || a.month.localeCompare(b.month));
    });
  }, []);

  const removeMonthlyStartingCapitalOverride = useCallback((month: string, year: number) => {
    const normalizedMonth = month.length > 3 ?
      ({ "January": "Jan", "February": "Feb", "March": "Mar", "April": "Apr", "May": "May", "June": "Jun",
         "July": "Jul", "August": "Aug", "September": "Sep", "October": "Oct", "November": "Nov", "December": "Dec" }[month] || month) :
      month;

    setMonthlyStartingCapitalOverrides(prev =>
      prev.filter(item => !(item.month === normalizedMonth && item.year === year))
    );
  }, []);

  const getMonthlyStartingCapitalOverride = useCallback((month: string, year: number): number | null => {
    // CRITICAL FIX: This function should work even during hydration since it's used for display
    const normalizedMonth = month.length > 3 ?
      ({ "January": "Jan", "February": "Feb", "March": "Mar", "April": "Apr", "May": "May", "June": "Jun",
         "July": "Jul", "August": "Aug", "September": "Sep", "October": "Oct", "November": "Nov", "December": "Dec" }[month] || month) :
      month;

    const override = monthlyStartingCapitalOverrides.find(item => item.month === normalizedMonth && item.year === year);
    return override ? override.startingCapital : null;
  }, [monthlyStartingCapitalOverrides]);

  const addCapitalChange = useCallback((change: Omit<CapitalChange, 'id'>) => {
    // CRITICAL FIX: Use proper UUID format instead of capital_ prefix
    const newChange = {
      ...change,
      id: uuidv4()
    };

    setCapitalChanges(prev => [...prev, newChange]);
  }, []);

  const updateCapitalChange = useCallback((updatedChange: CapitalChange) => {
    setCapitalChanges(prev =>
      prev.map(change =>
        change.id === updatedChange.id ? updatedChange : change
      )
    );
  }, []);

  const deleteCapitalChange = useCallback((id: string) => {
    setCapitalChanges(prev => prev.filter(change => change.id !== id));
  }, []);

  // CLEANUP TOOL: Remove duplicate entries
  const cleanupDuplicates = useCallback(async () => {
    // Debug logging removed for production

    // 1. Clean up duplicate yearly starting capitals
    const uniqueYearlyCapitals = new Map<number, YearlyStartingCapital>();
    yearlyStartingCapitals.forEach(capital => {
      const existing = uniqueYearlyCapitals.get(capital.year);
      if (!existing || new Date(capital.updatedAt) > new Date(existing.updatedAt)) {
        uniqueYearlyCapitals.set(capital.year, capital);
      }
    });
    const cleanYearlyCapitals = Array.from(uniqueYearlyCapitals.values());

    // Debug logging removed for production

    // 2. Clean up duplicate capital changes (keep only unique date+amount combinations)
    const uniqueCapitalChanges = new Map<string, CapitalChange>();
    capitalChanges.forEach(change => {
      const key = `${change.date}-${change.amount}-${change.type}`;
      const existing = uniqueCapitalChanges.get(key);
      if (!existing) {
        uniqueCapitalChanges.set(key, change);
      }
    });
    const cleanCapitalChanges = Array.from(uniqueCapitalChanges.values());

    // Debug logging removed for production

    // 3. Clean up duplicate monthly overrides
    const uniqueMonthlyOverrides = new Map<string, MonthlyStartingCapitalOverride>();
    monthlyStartingCapitalOverrides.forEach(override => {
      const key = `${override.month}-${override.year}`;
      const existing = uniqueMonthlyOverrides.get(key);
      if (!existing || new Date(override.updatedAt) > new Date(existing.updatedAt)) {
        uniqueMonthlyOverrides.set(key, override);
      }
    });
    const cleanMonthlyOverrides = Array.from(uniqueMonthlyOverrides.values());

    // Debug logging removed for production

    // Apply the cleaned data
    setYearlyStartingCapitals(cleanYearlyCapitals);
    setCapitalChanges(cleanCapitalChanges);
    setMonthlyStartingCapitalOverrides(cleanMonthlyOverrides);

    // Debug logging removed for production

    // Return summary
    return {
      yearlyCapitals: { before: yearlyStartingCapitals.length, after: cleanYearlyCapitals.length },
      capitalChanges: { before: capitalChanges.length, after: cleanCapitalChanges.length },
      monthlyOverrides: { before: monthlyStartingCapitalOverrides.length, after: cleanMonthlyOverrides.length }
    };
  }, [yearlyStartingCapitals, capitalChanges, monthlyStartingCapitalOverrides]);

  // Helper function to get trades P&L for a specific month/year
  const getTradesPLForMonth = useCallback((month: string, year: number, trades: any[] = [], useCashBasis: boolean = false): number => {
    if (!trades || trades.length === 0) return 0;

    if (useCashBasis) {
      // Cash basis: P&L is attributed to the month when trades are exited/closed
      const result = trades
        .filter(trade => {
          // Only include trades that have exits (closed or partial)
          return trade.positionStatus === 'Closed' || trade.positionStatus === 'Partial';
        })
        .reduce((sum, trade) => {
          let monthPL = 0;

          // Check each exit and attribute P&L to the respective exit months
          const exits = getExitDatesWithFallback(trade);

          // Calculate P&L for exits in this specific month/year
          exits.forEach(exit => {
            const exitDate = new Date(exit.date);
            // Use consistent month name conversion
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const exitMonth = monthNames[exitDate.getMonth()];
            const exitYear = exitDate.getFullYear();

            if (exitMonth === month && exitYear === year) {
              // Calculate P&L for this specific exit
              const avgEntry = trade.avgEntry || trade.entry || 0;
              if (avgEntry > 0 && exit.price > 0 && exit.qty > 0) {
                const exitPL = trade.buySell === 'Buy'
                  ? (exit.price - avgEntry) * exit.qty
                  : (avgEntry - exit.price) * exit.qty;
                monthPL += exitPL;
              }
            }
          });

          return sum + monthPL;
        }, 0);

      return result;
    } else {
      // Accrual basis: P&L is attributed to the month when trades are initiated (current behavior)
      return trades
        .filter(trade => {
          if (!trade.date) return false;
          const tradeDate = new Date(trade.date);
          // Use consistent month name conversion
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          const tradeMonth = monthNames[tradeDate.getMonth()];
          const tradeYear = tradeDate.getFullYear();
          return tradeMonth === month && tradeYear === year;
        })
        .reduce((sum, trade) => {
          // Use plRs if available, otherwise calculate basic P&L
          if (trade.plRs !== undefined && trade.plRs !== null) {
            return sum + trade.plRs;
          }
          // Fallback calculation for trades without plRs
          const exitedQty = trade.exitedQty || 0;
          const avgExitPrice = trade.avgExitPrice || 0;
          const avgEntry = trade.avgEntry || trade.entry || 0;
          if (exitedQty > 0 && avgExitPrice > 0 && avgEntry > 0) {
            const pl = trade.buySell === 'Buy'
              ? (avgExitPrice - avgEntry) * exitedQty
              : (avgEntry - avgExitPrice) * exitedQty;
            return sum + pl;
          }
          return sum;
        }, 0);
    }
  }, []);

  // Helper function to get capital changes for a specific month/year
  const getCapitalChangesForMonth = useCallback((month: string, year: number): number => {
    return capitalChanges
      .filter(change => {
        if (!change.date) return false;
        const changeDate = new Date(change.date);
        // Use consistent month name conversion
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const changeMonth = monthNames[changeDate.getMonth()];
        const changeYear = changeDate.getFullYear();
        return changeMonth === month && changeYear === year;
      })
      .reduce((sum, change) => {
        // CRITICAL FIX: Use the amount directly since we now store signed values
        // Positive amounts = deposits, Negative amounts = withdrawals
        return sum + change.amount;
      }, 0);
  }, [capitalChanges]);

  // Helper function to normalize month names
  const normalizeMonth = useCallback((month: string): string => {
    // If it's already a short month name, return as is
    const shortMonths = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    if (shortMonths.includes(month)) {
      return month;
    }

    // Convert full month names to short month names
    const monthMap: Record<string, string> = {
      "January": "Jan", "February": "Feb", "March": "Mar", "April": "Apr",
      "May": "May", "June": "Jun", "July": "Jul", "August": "Aug",
      "September": "Sep", "October": "Oct", "November": "Nov", "December": "Dec"
    };

    return monthMap[month] || month;
  }, []);

  // Core function to calculate monthly true portfolio with memoization
  const calculateMonthlyTruePortfolio = useCallback((month: string, year: number, trades: any[] = [], memo: Map<string, MonthlyTruePortfolio> = new Map(), minOverallDate: Date | null = null, useCashBasis: boolean = false): MonthlyTruePortfolio => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // Normalize the month name
    const normalizedMonth = normalizeMonth(month);
    const monthIndex = months.indexOf(normalizedMonth);

    if (monthIndex === -1) {
      throw new Error(`Invalid month: ${month}. Expected short month names like 'Jan', 'Feb', etc.`);
    }

    const key = `${normalizedMonth}-${year}`;
    if (memo.has(key)) {
      return memo.get(key)!;
    }

    let startingCapital = 0;

    const currentMonthDate = new Date(year, monthIndex, 1);

    // CRITICAL FIX: Completely remove the minOverallDate blocking for inheritance
    // The inheritance logic should work regardless of when trades start
    // Only block if we're going way too far back (before 2000)
    if (minOverallDate && currentMonthDate < minOverallDate && year < 2000) {
      return {
        month: normalizedMonth,
        year,
        startingCapital: 0,
        capitalChanges: 0,
        pl: 0,
        finalCapital: 0
      };
    }

    // Check for monthly starting capital override first
    const override = getMonthlyStartingCapitalOverride(normalizedMonth, year);
    if (override !== null) {
      startingCapital = override;
    } else if (normalizedMonth === 'Jan') {
      // CRITICAL FIX: For January, prioritize inheritance over yearly starting capital
      // This ensures December final capital carries forward to January

      const prevMonthIndex = monthIndex - 1;
      let prevMonth = months[prevMonthIndex];
      let prevYear = year;

      if (prevMonthIndex < 0) {
        prevMonth = months[11]; // December of previous year
        prevYear = year - 1;
      }

      try {
        // Try to inherit from previous December first
        const prevMonthData = calculateMonthlyTruePortfolio(prevMonth, prevYear, trades, memo, minOverallDate, useCashBasis);

        // If previous month has meaningful data (not zero), inherit it
        if (prevMonthData.finalCapital > 0) {
          startingCapital = prevMonthData.finalCapital;
        } else {
          // Only use yearly starting capital if no previous data exists
          const yearlyCapital = getYearlyStartingCapital(year);
          startingCapital = yearlyCapital > 0 ? yearlyCapital : 0;
        }
      } catch (error) {
        // If inheritance fails (e.g., first year), use yearly starting capital
        const yearlyCapital = getYearlyStartingCapital(year);
        startingCapital = yearlyCapital > 0 ? yearlyCapital : 0;
      }
    } else {
      // CRITICAL FIX: For all other months, inherit from previous month regardless of minOverallDate
      // This ensures proper inheritance even when there are no trades in early months
      const prevMonthIndex = monthIndex - 1;
      let prevMonth = months[prevMonthIndex];
      let prevYear = year;

      if (prevMonthIndex < 0) {
        prevMonth = months[11]; // December of previous year
        prevYear = year - 1;
      }

      const prevMonthData = calculateMonthlyTruePortfolio(prevMonth, prevYear, trades, memo, minOverallDate, useCashBasis);
      startingCapital = prevMonthData.finalCapital;
    }

    // Get capital changes for this month
    const capitalChangesAmount = getCapitalChangesForMonth(normalizedMonth, year);

    // CRITICAL FIX: Apply capital changes to starting capital in the SAME month
    // This ensures capital changes affect the month they're added to, not the next month
    const adjustedStartingCapital = startingCapital + capitalChangesAmount;

    // Get P&L for this month
    const pl = getTradesPLForMonth(normalizedMonth, year, trades, useCashBasis);

    // Final capital = adjusted starting capital + P&L
    const finalCapital = adjustedStartingCapital + pl;

    const result: MonthlyTruePortfolio = {
      month: normalizedMonth, // Always return normalized month name
      year,
      startingCapital: adjustedStartingCapital, // CRITICAL FIX: Starting capital includes capital changes
      capitalChanges: capitalChangesAmount,
      pl,
      finalCapital
    };

    memo.set(key, result);
    return result;
  }, [getYearlyStartingCapital, getCapitalChangesForMonth, getTradesPLForMonth, normalizeMonth, getMonthlyStartingCapitalOverride]);

  // Public function to get monthly true portfolio
  const getMonthlyTruePortfolio = useCallback((month: string, year: number, trades: any[] = [], useCashBasis: boolean = false): MonthlyTruePortfolio => {
    const memo = new Map<string, MonthlyTruePortfolio>();

    // Determine the earliest and latest dates with data (trades or capital changes)
    let minOverallDate: Date | null = null;

    [...trades, ...capitalChanges].forEach(item => {
        if (item.date) {
            const itemDate = new Date(item.date);
            if (!minOverallDate || itemDate < minOverallDate) {
                minOverallDate = itemDate;
            }
        }
    });

    // CRITICAL FIX: Consider yearly starting capitals for the earliest date
    // This ensures that months with yearly starting capital are not considered "before data"
    yearlyStartingCapitals.forEach(capital => {
        // Validate year to prevent absurd dates
        if (capital.year && capital.year >= 2000 && capital.year <= 2100) {
            const capitalDate = new Date(capital.year, 0, 1); // January 1st of the capital year
            if (!minOverallDate || capitalDate < minOverallDate) {
                minOverallDate = capitalDate;
            }
        } else {
            // Debug logging removed for production
        }
    });

    // If there's no data at all, fallback to current year
    if (!minOverallDate) {
        minOverallDate = new Date(new Date().getFullYear(), 0, 1); // January 1st of current year
    }

    // Adjust minOverallDate to the beginning of its month
    minOverallDate.setDate(1);
    minOverallDate.setHours(0, 0, 0, 0);

    return calculateMonthlyTruePortfolio(month, year, trades, memo, minOverallDate, useCashBasis);
  }, [calculateMonthlyTruePortfolio, yearlyStartingCapitals, capitalChanges]);

  // Get true portfolio size for a specific month/year
  const getTruePortfolioSize = useCallback((month: string, year: number, trades: any[] = [], useCashBasis: boolean = false): number => {
    try {
      const monthlyData = getMonthlyTruePortfolio(month, year, trades, useCashBasis);
      return monthlyData.finalCapital;
    } catch (error) {
      // Debug logging removed for production
      return 100000; // Fallback value
    }
  }, [getMonthlyTruePortfolio]);

  // CRITICAL FIX: Stabilize latest portfolio size calculation
  const getLatestTruePortfolioSize = useCallback((trades: any[] = [], useCashBasis: boolean = false): number => {
    try {
      const currentDate = new Date();
      const currentMonth = currentDate.toLocaleString('default', { month: 'short' });
      const currentYear = currentDate.getFullYear();

      const calculatedSize = getTruePortfolioSize(currentMonth, currentYear, trades, useCashBasis);

      // CRITICAL FIX: Ensure stable fallback and validation
      if (typeof calculatedSize === 'number' && calculatedSize > 0 && isFinite(calculatedSize)) {
        return calculatedSize;
      }

      // Fallback to yearly starting capital if available
      const yearlyCapital = getYearlyStartingCapital(currentYear);
      return yearlyCapital > 0 ? yearlyCapital : 100000;
    } catch (error) {
      // Debug logging removed for production
      return 100000; // Fallback value
    }
  }, [getTruePortfolioSize, getYearlyStartingCapital]);

  // Get all monthly true portfolios for a year or range
  const getAllMonthlyTruePortfolios = useCallback((trades: any[] = [], useCashBasis: boolean = false): MonthlyTruePortfolio[] => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const result: MonthlyTruePortfolio[] = [];
    const memo = new Map<string, MonthlyTruePortfolio>();

    // Determine the earliest and latest dates with data (trades or capital changes)
    let minOverallDate: Date | null = null;
    let maxOverallDate: Date | null = null;

    // Process trades - consider both entry and exit dates for cash basis
    trades.forEach(trade => {
        if (trade.date) {
            const entryDate = new Date(trade.date);
            // Validate entry date: must be valid and within reasonable range (2000-2100)
            if (!isNaN(entryDate.getTime()) &&
                entryDate.getFullYear() >= 2000 &&
                entryDate.getFullYear() <= 2100) {
                if (!minOverallDate || entryDate < minOverallDate) {
                    minOverallDate = entryDate;
                }
                if (!maxOverallDate || entryDate > maxOverallDate) {
                    maxOverallDate = entryDate;
                }
            } else {
                // Debug logging removed for production
            }
        }

        // For cash basis, also consider exit dates
        if (useCashBasis && (trade.positionStatus === 'Closed' || trade.positionStatus === 'Partial')) {
            [trade.exit1Date, trade.exit2Date, trade.exit3Date].forEach(exitDate => {
                if (exitDate) {
                    const exitDateObj = new Date(exitDate);
                    // Validate date: must be valid and within reasonable range (2000-2100)
                    if (!isNaN(exitDateObj.getTime()) &&
                        exitDateObj.getFullYear() >= 2000 &&
                        exitDateObj.getFullYear() <= 2100) {
                        if (!minOverallDate || exitDateObj < minOverallDate) {
                            minOverallDate = exitDateObj;
                        }
                        if (!maxOverallDate || exitDateObj > maxOverallDate) {
                            maxOverallDate = exitDateObj;
                        }
                    } else {
                        // Debug logging removed for production
                    }
                }
            });
        }
    });

    // Process capital changes
    capitalChanges.forEach(item => {
        if (item.date) {
            const itemDate = new Date(item.date);
            // Validate capital change date: must be valid and within reasonable range (2000-2100)
            if (!isNaN(itemDate.getTime()) &&
                itemDate.getFullYear() >= 2000 &&
                itemDate.getFullYear() <= 2100) {
                if (!minOverallDate || itemDate < minOverallDate) {
                    minOverallDate = itemDate;
                }
                if (!maxOverallDate || itemDate > maxOverallDate) {
                    maxOverallDate = itemDate;
                }
            } else {
                // Debug logging removed for production
            }
        }
    });

    // Also consider yearly starting capitals for the earliest date
    yearlyStartingCapitals.forEach(capital => {
        // Validate year to prevent absurd dates
        if (capital.year && capital.year >= 2000 && capital.year <= 2100) {
            const capitalDate = new Date(capital.year, 0, 1); // January 1st of the capital year
            if (!minOverallDate || capitalDate < minOverallDate) {
                minOverallDate = capitalDate;
            }
        } else {
            // Debug logging removed for production
        }
    });

    // If there's no data at all, fallback to current year
    if (!minOverallDate) {
        minOverallDate = new Date(new Date().getFullYear(), 0, 1); // January 1st of current year
    }

    // Adjust minOverallDate to the beginning of its month
    minOverallDate.setDate(1);
    minOverallDate.setHours(0, 0, 0, 0);

    // If maxOverallDate is not set (e.g., only yearly capital with no trades/capital changes), default to current date
    if (!maxOverallDate) {
      maxOverallDate = new Date();
    }

    // Ensure maxOverallDate is at the end of its month to include all trades/changes within that month
    maxOverallDate.setMonth(maxOverallDate.getMonth() + 1);
    maxOverallDate.setDate(0); // This sets it to the last day of the previous month
    maxOverallDate.setHours(23, 59, 59, 999);

    let currentDate = new Date(minOverallDate.getFullYear(), minOverallDate.getMonth(), 1);

    while (currentDate <= maxOverallDate) {
        const year = currentDate.getFullYear();
        const month = months[currentDate.getMonth()]; // Get short month name

        try {
            const monthlyData = calculateMonthlyTruePortfolio(month, year, trades, memo, minOverallDate, useCashBasis); // Pass minOverallDate and useCashBasis
            result.push(monthlyData);
        } catch (error) {
            // Skip months with no data
        }

        currentDate.setMonth(currentDate.getMonth() + 1);
    }

    return result;
  }, [yearlyStartingCapitals, capitalChanges, calculateMonthlyTruePortfolio]);

  // CRITICAL FIX: Stabilize portfolio size with better memoization
  const portfolioSize = React.useMemo(() => {
    try {
      const size = getLatestTruePortfolioSize();

      // CRITICAL FIX: Add validation and stability checks
      if (typeof size === 'number' && size > 0 && isFinite(size)) {
        return size;
      }

      // Fallback to first available yearly starting capital
      const currentYear = new Date().getFullYear();
      const yearlyCapital = getYearlyStartingCapital(currentYear);
      return yearlyCapital > 0 ? yearlyCapital : 100000;
    } catch (error) {
      // Debug logging removed for production
      return 100000; // Fallback value
    }
  }, [getLatestTruePortfolioSize, getYearlyStartingCapital, yearlyStartingCapitals]);

  // CRITICAL FIX: Create safe wrapper functions that try to calculate even during hydration
  const safeGetTruePortfolioSize = useCallback((month: string, year: number, trades?: any[], useCashBasis?: boolean) => {
    // CRITICAL FIX: Try to calculate even during hydration if we have the necessary data
    if (!hydrated) {
      // Check if we have yearly starting capital data available
      const yearlyCapital = yearlyStartingCapitals.find(c => c.year === year);
      if (yearlyCapital && month === 'Jan') {
        return yearlyCapital.startingCapital;
      }

      // Check if we have monthly override data available
      const override = monthlyStartingCapitalOverrides.find(o => o.month === month && o.year === year);
      if (override) {
        return override.startingCapital;
      }

      return 0; // Return 0 instead of 100000 to avoid confusion
    }

    return getTruePortfolioSize(month, year, trades, useCashBasis);
  }, [hydrated, getTruePortfolioSize, yearlyStartingCapitals, monthlyStartingCapitalOverrides]);

  const safeGetLatestTruePortfolioSize = useCallback((trades?: any[], useCashBasis?: boolean) => {
    if (!hydrated) return 100000; // Return default value during hydration
    return getLatestTruePortfolioSize(trades, useCashBasis);
  }, [hydrated, getLatestTruePortfolioSize]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    getTruePortfolioSize: safeGetTruePortfolioSize,
    getLatestTruePortfolioSize: safeGetLatestTruePortfolioSize,
    yearlyStartingCapitals,
    setYearlyStartingCapital,
    getYearlyStartingCapital,
    monthlyStartingCapitalOverrides,
    setMonthlyStartingCapitalOverride,
    removeMonthlyStartingCapitalOverride,
    getMonthlyStartingCapitalOverride,
    capitalChanges,
    addCapitalChange,
    updateCapitalChange,
    deleteCapitalChange,
    getMonthlyTruePortfolio,
    getAllMonthlyTruePortfolios,
    cleanupDuplicates,
    portfolioSize: hydrated ? portfolioSize : 100000
  }), [
    safeGetTruePortfolioSize,
    safeGetLatestTruePortfolioSize,
    yearlyStartingCapitals,
    setYearlyStartingCapital,
    getYearlyStartingCapital,
    monthlyStartingCapitalOverrides,
    setMonthlyStartingCapitalOverride,
    removeMonthlyStartingCapitalOverride,
    getMonthlyStartingCapitalOverride,
    capitalChanges,
    addCapitalChange,
    updateCapitalChange,
    deleteCapitalChange,
    getMonthlyTruePortfolio,
    getAllMonthlyTruePortfolios,
    cleanupDuplicates,
    portfolioSize,
    hydrated
  ]);

  // DEVELOPMENT HELPER: Expose cleanup function to window for console access
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).cleanupDuplicates = cleanupDuplicates;
    }
  }, [cleanupDuplicates]);

  // Always render children to prevent hook count mismatches
  return (
    <TruePortfolioContext.Provider value={contextValue}>
      {children}
    </TruePortfolioContext.Provider>
  );
};

export const useTruePortfolio = (): TruePortfolioContextType => {
  const ctx = useContext(TruePortfolioContext);
  if (!ctx) throw new Error("useTruePortfolio must be used within a TruePortfolioProvider");
  return ctx;
};
