import { supabase } from '../lib/supabase'
import type { User, Session, AuthError } from '@supabase/supabase-js'
import { SupabaseService } from './supabaseService'

export interface AuthState {
  user: User | null
  session: Session | null
  loading: boolean
  error: string | null
}

export interface SignUpData {
  email: string
  password: string
  firstName?: string
  lastName?: string
}

export interface SignInData {
  email: string
  password: string
}

export class AuthService {
  // CRITICAL FIX: Enhanced JWT caching to prevent 2.8M+ auth calls
  private static userIdCache: string | null = null
  private static sessionCache: Session | null = null
  private static lastAuthCheck = 0
  private static AUTH_CACHE_DURATION = 10 * 60 * 1000 // Increased to 10 minutes
  private static authPromise: Promise<string | null> | null = null // Prevent concurrent calls
  private static sessionPromise: Promise<Session | null> | null = null
  private static isInitialized = false
  private static callCount = 0 // Debug counter
  private static authStateListenerSetup = false
  private static authStateDebounceTimeout: NodeJS.Timeout | null = null

  /**
   * Setup auth state listener to reactively track auth changes
   */
  private static setupAuthStateListener(): void {
    if (this.authStateListenerSetup) return;

    supabase.auth.onAuthStateChange((event, session) => {
      // FIXED: Start new session on auth changes to clear stale cache
      if (event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
        SupabaseService.startNewSession();
      }

      // Debounce rapid auth state changes to prevent excessive calls
      if (this.authStateDebounceTimeout) {
        clearTimeout(this.authStateDebounceTimeout);
      }

      this.authStateDebounceTimeout = setTimeout(() => {
        // Update caches immediately
        this.sessionCache = session;
        this.userIdCache = session?.user?.id || null;
        this.lastAuthCheck = Date.now();

        // Clear any pending promises to force fresh data
        this.authPromise = null;
        this.sessionPromise = null;
      }, 100); // 100ms debounce to prevent rapid-fire auth state changes
    });

    this.authStateListenerSetup = true;
  }

  /**
   * Initialize authentication - call this once at app startup
   */
  static async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Setup reactive auth state listener first
      this.setupAuthStateListener();

      // Pre-populate the cache
      await this.getUserId();
      this.isInitialized = true;
      console.log('🔐 Authentication service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize authentication service:', error);
    }
  }

  /**
   * Sign up a new user with email and password
   */
  static async signUp({ email, password, firstName, lastName }: SignUpData) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
            full_name: firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName || '',
          }
        }
      })

      if (error) {
        throw error
      }

      return { data, error: null }
    } catch (error) {
      return { data: null, error: error as AuthError }
    }
  }

  /**
   * Sign in an existing user with email and password
   */
  static async signIn({ email, password }: SignInData) {
    try {
      // Debug logging removed for production performance

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        console.error('❌ Sign in error:', error.message)
        throw error
      }

      // Debug logging removed for production performance
      return { data, error: null }
    } catch (error) {
      const authError = error as AuthError
      console.error('❌ Sign in failed:', authError.message)
      return { data: null, error: authError }
    }
  }

  /**
   * Sign in with OAuth provider (Twitter, Google)
   */
  static async signInWithProvider(provider: 'twitter' | 'google') {
    try {
      // Use environment variable for redirect URL, fallback to production domain
      const redirectTo = (import.meta as any).env.VITE_OAUTH_REDIRECT_URL || 'https://www.nexusjournal.in/auth/callback';

      // For client-side OAuth, use the simple approach from documentation
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo
        }
      })

      if (error) {
        throw error
      }

      return { data, error: null }
    } catch (error) {
      return { data: null, error: error as AuthError }
    }
  }

  /**
   * Sign out the current user
   */
  static async signOut() {
    try {
      console.log('🔐 Starting logout process...')

      // CRITICAL FIX: Use 'local' scope to ensure complete logout
      const { error } = await supabase.auth.signOut({ scope: 'local' })

      if (error) {
        console.error('🔐 Logout error:', error)
        throw error
      }

      // Clear auth cache and promise on sign out
      this.userIdCache = null
      this.lastAuthCheck = 0
      this.authPromise = null

      // CRITICAL FIX: Clear all caches on logout for security
      const { SupabaseService } = await import('./supabaseService');
      SupabaseService.invalidateCachesByOperation('logout');
      console.log('🗑️ All caches cleared on logout');

      console.log('🔐 Logout process completed successfully')
      return { error: null }
    } catch (error) {
      console.error('🔐 Logout process failed:', error)
      return { error: error as AuthError }
    }
  }

  /**
   * Send password reset email
   */
  static async resetPassword(email: string) {
    try {
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      })

      if (error) {
        throw error
      }

      return { data, error: null }
    } catch (error) {
      return { data: null, error: error as AuthError }
    }
  }

  /**
   * Update user password
   */
  static async updatePassword(newPassword: string) {
    try {
      const { data, error } = await supabase.auth.updateUser({
        password: newPassword
      })

      if (error) {
        throw error
      }

      return { data, error: null }
    } catch (error) {
      return { data: null, error: error as AuthError }
    }
  }

  /**
   * Resend email verification
   */
  static async resendVerification(email: string) {
    try {
      const { data, error } = await supabase.auth.resend({
        type: 'signup',
        email: email
      })

      if (error) {
        throw error
      }

      return { data, error: null }
    } catch (error) {
      return { data: null, error: error as AuthError }
    }
  }

  /**
   * Check if user exists (for better error messaging)
   */
  static async checkUserExists(email: string): Promise<boolean> {
    try {
      // Try to initiate password reset - this will tell us if user exists
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      })

      // If no error, user exists
      // If error contains "User not found", user doesn't exist
      if (error && error.message.includes('User not found')) {
        return false
      }

      return true
    } catch (error) {
      // If there's an error, assume user doesn't exist
      return false
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(updates: {
    email?: string
    firstName?: string
    lastName?: string
  }) {
    try {
      const { data, error } = await supabase.auth.updateUser({
        email: updates.email,
        data: {
          first_name: updates.firstName,
          last_name: updates.lastName,
          full_name: updates.firstName && updates.lastName
            ? `${updates.firstName} ${updates.lastName}`
            : updates.firstName || updates.lastName || '',
        }
      })

      if (error) {
        throw error
      }

      return { data, error: null }
    } catch (error) {
      return { data: null, error: error as AuthError }
    }
  }

  /**
   * Get current user
   */
  static async getCurrentUser() {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error) {
        // Don't log session missing errors as they're expected on initial load
        if (error.message !== 'Auth session missing!') {
          console.error('Auth error getting user:', error)
        }
        throw error
      }

      return { user, error: null }
    } catch (error) {
      // Only log non-session-missing errors
      const authError = error as AuthError
      if (authError.message !== 'Auth session missing!') {
        console.error('Auth error in getCurrentUser:', authError)
      }
      return { user: null, error: authError }
    }
  }

  /**
   * Get current session with caching (CRITICAL FIX for 283,644 session calls)
   */
  static async getCurrentSession() {
    const now = Date.now();

    // Return cached session if still valid
    if (this.sessionCache !== null && (now - this.lastAuthCheck) < this.AUTH_CACHE_DURATION) {
      return { session: this.sessionCache, error: null };
    }

    // If there's already a session request in progress, wait for it
    if (this.sessionPromise) {
      const session = await this.sessionPromise;
      return { session, error: null };
    }

    // Create new session promise
    this.sessionPromise = this.performSessionCheck();

    try {
      const session = await this.sessionPromise;
      return { session, error: null };
    } catch (error) {
      return { session: null, error: error as AuthError };
    } finally {
      this.sessionPromise = null;
    }
  }

  private static async performSessionCheck(): Promise<Session | null> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Session check error:', error);
        return null;
      }

      // Update cache
      this.sessionCache = session;
      this.userIdCache = session?.user?.id || null;
      this.lastAuthCheck = Date.now();

      return session;
    } catch (error) {
      console.error('Session check failed:', error);
      return null;
    }
  }

  /**
   * Listen to auth state changes
   */
  static onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return supabase.auth.onAuthStateChange(callback)
  }

  /**
   * Check if user is authenticated (uses cache to prevent excessive calls)
   */
  static async isAuthenticated(): Promise<boolean> {
    // Setup auth listener on first call
    this.setupAuthStateListener();

    // Check cache first
    const now = Date.now();
    if (this.sessionCache !== null && (now - this.lastAuthCheck) < this.AUTH_CACHE_DURATION) {
      return !!this.sessionCache;
    }

    // Only fetch from server if cache is stale
    const { session } = await this.getCurrentSession();
    return !!session;
  }

  /**
   * Get current user (uses cached session to prevent excessive getUser() calls)
   */
  static async getCurrentUser() {
    try {
      // Use cached session instead of making separate getUser() call
      const { session } = await this.getCurrentSession();
      const user = session?.user || null;

      return { user, error: null };
    } catch (error) {
      const authError = error as AuthError;
      if (authError.message !== 'Auth session missing!') {
        console.error('Auth error in getCurrentUser:', authError);
      }
      return { user: null, error: authError };
    }
  }

  /**
   * Get user ID if authenticated (with caching and concurrency protection)
   */
  static async getUserId(): Promise<string | null> {
    this.callCount++;
    const now = Date.now()

    // Return cached result if still valid
    if (this.userIdCache !== null && (now - this.lastAuthCheck) < this.AUTH_CACHE_DURATION) {
      return this.userIdCache
    }

    // If there's already an auth request in progress, wait for it
    if (this.authPromise) {
      return this.authPromise
    }

    // Create new auth promise
    this.authPromise = this.performAuthCheck()

    try {
      const result = await this.authPromise
      return result
    } finally {
      this.authPromise = null
    }
  }

  private static async performAuthCheck(): Promise<string | null> {
    const now = Date.now()

    try {
      const { user } = await this.getCurrentUser()
      const userId = user?.id || null

      // Update cache
      this.userIdCache = userId
      this.lastAuthCheck = now

      // Debug logging removed for production performance
      // Only log errors or significant events

      return userId
    } catch (error) {
      console.error('❌ Authentication check failed:', error)
      return null
    }
  }

  /**
   * Refresh session
   */
  static async refreshSession() {
    try {
      const { data, error } = await supabase.auth.refreshSession()

      if (error) {
        throw error
      }

      return { data, error: null }
    } catch (error) {
      return { data: null, error: error as AuthError }
    }
  }
}

// Export auth state management utilities
export const getAuthState = async (): Promise<AuthState> => {
  try {
    const { session } = await AuthService.getCurrentSession()
    const { user } = await AuthService.getCurrentUser()

    return {
      user,
      session,
      loading: false,
      error: null
    }
  } catch (error) {
    const authError = error as AuthError
    // Don't treat session missing as an error state
    if (authError.message === 'Auth session missing!') {
      return {
        user: null,
        session: null,
        loading: false,
        error: null
      }
    }

    return {
      user: null,
      session: null,
      loading: false,
      error: authError.message
    }
  }
}

// Helper function to handle auth errors
export const getAuthErrorMessage = (error: AuthError | null): string => {
  if (!error) return ''

  console.log('🔍 Processing auth error:', error.message)

  switch (error.message) {
    case 'Invalid login credentials':
      return 'INVALID_CREDENTIALS'
    case 'Email not confirmed':
    case 'Email link is invalid or has expired':
    case 'Signup requires a valid password':
      return 'EMAIL_NOT_CONFIRMED'
    case 'User already registered':
      return 'An account with this email already exists. Please sign in instead.'
    case 'Password should be at least 6 characters':
      return 'Password must be at least 6 characters long.'
    case 'Unable to validate email address: invalid format':
      return 'Please enter a valid email address.'
    case 'Signup is disabled':
      return 'New user registration is currently disabled.'
    case 'For security purposes, you can only request this once every 60 seconds':
      return 'Please wait 60 seconds before requesting another verification email.'
    case 'Auth session missing!':
      return 'SESSION_MISSING'
    default:
      // Check for specific error patterns
      if (error.message.toLowerCase().includes('invalid login') ||
          error.message.toLowerCase().includes('invalid credentials') ||
          error.message.toLowerCase().includes('wrong password') ||
          error.message.toLowerCase().includes('incorrect password')) {
        return 'INVALID_CREDENTIALS'
      }

      // Check for email confirmation related errors
      if (error.message.toLowerCase().includes('confirm') ||
          error.message.toLowerCase().includes('verify') ||
          error.message.toLowerCase().includes('email')) {
        return 'EMAIL_NOT_CONFIRMED'
      }

      console.log('⚠️ Unhandled auth error:', error.message)
      return error.message || 'An unexpected error occurred. Please try again.'
  }
}
