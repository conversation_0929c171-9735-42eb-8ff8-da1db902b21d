import { SupabaseService } from './supabaseService';
import { AuthService } from './authService';

/**
 * Cache Preloader Service
 * Handles intelligent cache warming and preloading strategies
 */
export class CachePreloader {
  private static isInitialized = false;
  private static preloadingStrategy: 'aggressive' | 'balanced' | 'minimal' = 'balanced';
  private static preloadStats = {
    totalPreloads: 0,
    successfulPreloads: 0,
    failedPreloads: 0,
    totalTimeMs: 0
  };

  /**
   * Initialize the cache preloader
   * Call this once on app startup
   */
  static async initialize(strategy: 'aggressive' | 'balanced' | 'minimal' = 'balanced'): Promise<void> {
    if (this.isInitialized) return;

    this.preloadingStrategy = strategy;
    this.isInitialized = true;

    // Start preloading based on strategy
    await this.executePreloadingStrategy();
  }

  /**
   * Execute preloading based on selected strategy
   */
  private static async executePreloadingStrategy(): Promise<void> {
    const startTime = performance.now();

    try {
      switch (this.preloadingStrategy) {
        case 'aggressive':
          await this.aggressivePreload();
          break;
        case 'balanced':
          await this.balancedPreload();
          break;
        case 'minimal':
          await this.minimalPreload();
          break;
      }

      const endTime = performance.now();
      this.preloadStats.totalTimeMs = endTime - startTime;
      this.preloadStats.successfulPreloads++;

    } catch (error) {
      this.preloadStats.failedPreloads++;
    } finally {
      this.preloadStats.totalPreloads++;
    }
  }

  /**
   * Aggressive preloading - loads everything immediately
   * Best for users with fast connections and powerful devices
   */
  private static async aggressivePreload(): Promise<void> {
    // Load everything in parallel
    await Promise.allSettled([
      SupabaseService.preloadCache(),
      this.preloadAnalyticsData(),
      this.preloadMonthlyPerformanceData(),
      this.preloadDrawdownData(),
      this.preloadChartData(),
      this.preloadTaxData()
    ]);
  }

  /**
   * Balanced preloading - loads critical data first, then secondary data
   * Best for most users - good balance of speed and resource usage
   */
  private static async balancedPreload(): Promise<void> {
    // Phase 1: Critical data (blocking)
    await SupabaseService.preloadCache();

    // Phase 2: Secondary data (non-blocking)
    setTimeout(() => {
      Promise.allSettled([
        this.preloadAnalyticsData(),
        this.preloadMonthlyPerformanceData(),
        this.preloadTaxData()
      ]);
    }, 1000); // Delay secondary preloading by 1 second

    // Phase 3: Heavy data (further delayed)
    setTimeout(() => {
      Promise.allSettled([
        this.preloadDrawdownData(),
        this.preloadChartData()
      ]);
    }, 3000); // Delay heavy preloading by 3 seconds
  }

  /**
   * Minimal preloading - only loads essential data
   * Best for users with slow connections or limited resources
   */
  private static async minimalPreload(): Promise<void> {
    // Only load the most critical data
    const userId = await AuthService.getUserId();
    if (userId) {
      await Promise.allSettled([
        SupabaseService.getMiscData('accountingMethod'),
        SupabaseService.getMiscData('globalFilter')
      ]);
    }
  }

  /**
   * Preload analytics-related data
   */
  private static async preloadAnalyticsData(): Promise<void> {
    try {

      const currentYear = new Date().getFullYear();
      const analyticsPreloadTasks = [
        SupabaseService.getMiscData('performanceMetrics').catch(error => {
          console.warn('⚠️ Failed to preload performance metrics:', error);
          return null;
        }),
        SupabaseService.getMiscData('riskMetrics').catch(error => {
          console.warn('⚠️ Failed to preload risk metrics:', error);
          return null;
        }),
        SupabaseService.getPortfolioData(currentYear).catch(error => {
          console.warn('⚠️ Failed to preload portfolio data:', error);
          return null;
        }),
        SupabaseService.getAnalyticsData('performance').catch(error => {
          console.warn('⚠️ Failed to preload performance analytics:', error);
          return null;
        }),
        SupabaseService.getAnalyticsData('risk').catch(error => {
          console.warn('⚠️ Failed to preload risk analytics:', error);
          return null;
        }),
        SupabaseService.getAnalyticsData('metrics').catch(error => {
          console.warn('⚠️ Failed to preload metrics analytics:', error);
          return null;
        })
      ];

      await Promise.allSettled(analyticsPreloadTasks);

    } catch (error) {
      // Silently handle preload errors
    }
  }

  /**
   * Preload monthly performance data
   */
  private static async preloadMonthlyPerformanceData(): Promise<void> {
    try {

      const currentYear = new Date().getFullYear();
      const monthlyPerfTasks = [
        SupabaseService.getMonthlyPerformanceData(currentYear, 'cash').catch(error => {
          console.warn('⚠️ Failed to preload current year cash performance:', error);
          return null;
        }),
        SupabaseService.getMonthlyPerformanceData(currentYear, 'accrual').catch(error => {
          console.warn('⚠️ Failed to preload current year accrual performance:', error);
          return null;
        }),
        SupabaseService.getMonthlyPerformanceData(currentYear - 1, 'cash').catch(error => {
          console.warn('⚠️ Failed to preload previous year cash performance:', error);
          return null;
        }),
        SupabaseService.getMonthlyPerformanceData(currentYear - 1, 'accrual').catch(error => {
          console.warn('⚠️ Failed to preload previous year accrual performance:', error);
          return null;
        })
      ];

      await Promise.allSettled(monthlyPerfTasks);

    } catch (error) {
      // Silently handle preload errors
    }
  }

  /**
   * Preload drawdown analysis data
   */
  private static async preloadDrawdownData(): Promise<void> {
    try {

      await Promise.allSettled([
        SupabaseService.getDrawdownData('cash'),
        SupabaseService.getDrawdownData('accrual')
      ]);

    } catch (error) {
      // Silently handle preload errors
    }
  }

  /**
   * Preload chart-related data
   */
  private static async preloadChartData(): Promise<void> {
    try {

      const chartPreloadTasks = [
        // Preload recent chart metadata (last 10 charts)
        SupabaseService.getRecentChartImages(10).catch(() => []),
        // Preload chart viewer data with default filters
        SupabaseService.getChartViewerData().catch(() => null),
        // Preload chart statistics
        SupabaseService.getChartStatistics().catch(() => null)
      ];

      await Promise.allSettled(chartPreloadTasks);
    } catch (error) {
      // Silently handle preload errors
    }
  }

  /**
   * Preload tax-related data (DISABLED - tax_data table doesn't exist)
   */
  private static async preloadTaxData(): Promise<void> {
    try {
      // CRITICAL FIX: Only preload misc data, not tax_data table
      await Promise.allSettled([
        SupabaseService.getMiscData('taxData')
        // Removed: SupabaseService.getTaxData() calls - table doesn't exist
      ]);

    } catch (error) {
      // Silently handle preload errors
    }
  }

  /**
   * Smart preloading based on user behavior
   * Analyzes usage patterns and preloads accordingly
   */
  static async smartPreload(userBehavior: {
    frequentPages: string[];
    lastVisitedPage: string;
    sessionCount: number;
  }): Promise<void> {
    const { frequentPages, lastVisitedPage, sessionCount } = userBehavior;
    
    // New users get minimal preloading
    if (sessionCount < 3) {
      await this.minimalPreload();
      return;
    }
    
    // Frequent analytics users get analytics data preloaded
    if (frequentPages.includes('/analytics') || frequentPages.includes('/deep-analytics')) {
      await this.preloadAnalyticsData();
    }
    
    // Tax analytics users get tax data preloaded
    if (frequentPages.includes('/tax-analytics')) {
      await this.preloadTaxData();
    }
    
    // Always preload data for last visited page
    await this.preloadForPage(lastVisitedPage);
  }

  /**
   * Preload data specific to a page
   */
  private static async preloadForPage(page: string): Promise<void> {
    switch (page) {
      case '/analytics':
      case '/deep-analytics':
        await Promise.allSettled([
          this.preloadAnalyticsData(),
          this.preloadDrawdownData()
        ]);
        break;
      case '/tax-analytics':
        await this.preloadTaxData();
        break;
      case '/monthly-performance':
        await Promise.allSettled([
          this.preloadMonthlyPerformanceData(),
          SupabaseService.getMiscData('portfolioData')
        ]);
        break;
      case '/chart-viewer':
        await this.preloadChartData();
        break;
      default:
        // For main journal page, preload trades
        await SupabaseService.getAllTrades();
    }
  }

  /**
   * Get preloading statistics
   */
  static getStats() {
    return {
      ...this.preloadStats,
      strategy: this.preloadingStrategy,
      isInitialized: this.isInitialized,
      cacheStats: SupabaseService.getCacheStats()
    };
  }

  /**
   * Warm cache for specific data types
   */
  static async warmCache(dataTypes: string[]): Promise<void> {
    const warmingTasks = dataTypes.map(async (type) => {
      switch (type) {
        case 'trades':
          // Only warm if cache is empty or stale
          return SupabaseService.warmTradesCache();
        case 'analytics':
          return this.preloadAnalyticsData();
        case 'tax':
          return this.preloadTaxData();
        case 'charts':
          return this.preloadChartData();
        case 'monthly-performance':
          return this.preloadMonthlyPerformanceData();
        case 'drawdown':
          return this.preloadDrawdownData();
        case 'performance':
          return SupabaseService.getAnalyticsData('performance');
        case 'risk':
          return SupabaseService.getAnalyticsData('risk');
        case 'metrics':
          return SupabaseService.getAnalyticsData('metrics');
        default:
          return Promise.resolve();
      }
    });

    await Promise.allSettled(warmingTasks);
  }

  /**
   * Reset preloader state
   */
  static reset(): void {
    this.isInitialized = false;
    this.preloadStats = {
      totalPreloads: 0,
      successfulPreloads: 0,
      failedPreloads: 0,
      totalTimeMs: 0
    };
  }
}
