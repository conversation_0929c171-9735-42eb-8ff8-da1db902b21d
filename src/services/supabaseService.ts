import { supabase } from '../lib/supabase'
import { AuthService } from './authService'
import type { Trade, ChartImage, CapitalChange } from '../types/trade'
import { v4 as uuidv4 } from 'uuid'
import { validateTradeForDatabase, sanitizeTradeForDatabase, validateTradesBatch } from '../utils/databaseValidation'

// Removed automatic hash function used for ID generation

// Removed automatic legacy ID to UUID conversion logic

// Helper function to convert database row to Trade object
const dbRowToTrade = (row: any): Trade => {
  // Use ID as-is (no automatic conversion)
  const originalId = row.id

  return {
    id: originalId,
    tradeNo: row.trade_no,
    date: row.date,
    name: row.name,
    entry: Number(row.entry || 0),
    avgEntry: Number(row.avg_entry || 0),
    sl: Number(row.sl || 0),
    tsl: Number(row.tsl || 0),
    buySell: row.buy_sell as 'Buy' | 'Sell',
    cmp: Number(row.cmp || 0),
    setup: row.setup || '',
    baseDuration: row.base_duration || '',
    initialQty: Number(row.initial_qty || 0),
    pyramid1Price: Number(row.pyramid1_price || 0),
    pyramid1Qty: Number(row.pyramid1_qty || 0),
    pyramid1Date: row.pyramid1_date || '',
    pyramid2Price: Number(row.pyramid2_price || 0),
    pyramid2Qty: Number(row.pyramid2_qty || 0),
    pyramid2Date: row.pyramid2_date || '',
    positionSize: Number(row.position_size || 0),
    allocation: Number(row.allocation || 0),
    slPercent: Number(row.sl_percent || 0),
    exit1Price: Number(row.exit1_price || 0),
    exit1Qty: Number(row.exit1_qty || 0),
    exit1Date: row.exit1_date || '',
    exit2Price: Number(row.exit2_price || 0),
    exit2Qty: Number(row.exit2_qty || 0),
    exit2Date: row.exit2_date || '',
    exit3Price: Number(row.exit3_price || 0),
    exit3Qty: Number(row.exit3_qty || 0),
    exit3Date: row.exit3_date || '',
    openQty: Number(row.open_qty || 0),
    exitedQty: Number(row.exited_qty || 0),
    avgExitPrice: Number(row.avg_exit_price || 0),
    stockMove: Number(row.stock_move || 0),
    rewardRisk: Number(row.reward_risk || 0),
    holdingDays: Number(row.holding_days || 0),
    positionStatus: row.position_status as 'Open' | 'Closed' | 'Partial',
    realisedAmount: Number(row.realised_amount || 0),
    plRs: Number(row.pl_rs || 0),
    pfImpact: Number(row.pf_impact || 0),
    cummPf: Number(row.cumm_pf || 0),
    planFollowed: Boolean(row.plan_followed),
    exitTrigger: row.exit_trigger || '',
    proficiencyGrowthAreas: row.proficiency_growth_areas || '',
    sector: row.sector || '',
    openHeat: Number(row.open_heat || 0),
    notes: row.notes || '',
    chartAttachments: row.chart_attachments || {},
    _userEditedFields: row.user_edited_fields || [],
    _cmpAutoFetched: Boolean(row.cmp_auto_fetched),
    _needsRecalculation: Boolean(row.needs_recalculation),
  }
}

// Helper function to convert Trade object to database insert/update format
const tradeToDbRow = (trade: Trade, userId: string) => {
  // Use trade ID as-is or generate UUID if empty
  const id = trade.id || uuidv4()

  return {
    id: id,
    user_id: userId,
    trade_no: trade.tradeNo,
    date: trade.date,
    name: trade.name,
    entry: trade.entry,
    avg_entry: trade.avgEntry,
    sl: trade.sl,
    tsl: trade.tsl,
    buy_sell: trade.buySell,
    cmp: trade.cmp,
    setup: trade.setup,
    base_duration: trade.baseDuration,
    initial_qty: trade.initialQty,
    pyramid1_price: trade.pyramid1Price,
    pyramid1_qty: trade.pyramid1Qty,
    pyramid1_date: trade.pyramid1Date || null,
    pyramid2_price: trade.pyramid2Price,
    pyramid2_qty: trade.pyramid2Qty,
    pyramid2_date: trade.pyramid2Date || null,
    position_size: trade.positionSize,
    allocation: trade.allocation,
    sl_percent: trade.slPercent,
    exit1_price: trade.exit1Price,
    exit1_qty: trade.exit1Qty,
    exit1_date: trade.exit1Date || null,
    exit2_price: trade.exit2Price,
    exit2_qty: trade.exit2Qty,
    exit2_date: trade.exit2Date || null,
    exit3_price: trade.exit3Price,
    exit3_qty: trade.exit3Qty,
    exit3_date: trade.exit3Date || null,
    open_qty: trade.openQty,
    exited_qty: trade.exitedQty,
    avg_exit_price: trade.avgExitPrice,
    stock_move: trade.stockMove,
    reward_risk: trade.rewardRisk,
    holding_days: trade.holdingDays,
    position_status: trade.positionStatus,
    realised_amount: trade.realisedAmount,
    pl_rs: trade.plRs,
    pf_impact: trade.pfImpact,
    cumm_pf: trade.cummPf,
    plan_followed: trade.planFollowed,
    exit_trigger: trade.exitTrigger,
    proficiency_growth_areas: trade.proficiencyGrowthAreas,
    sector: trade.sector,
    open_heat: trade.openHeat,
    notes: trade.notes,
    chart_attachments: trade.chartAttachments || {},
    user_edited_fields: trade._userEditedFields || [],
    cmp_auto_fetched: trade._cmpAutoFetched || false,
    needs_recalculation: trade._needsRecalculation || false,
  }
}

export class SupabaseService {
  // ===== TRADES =====
  
  // Performance cache for trades
  private static tradesCache = new Map<string, { data: Trade[], timestamp: number }>();
  private static CACHE_DURATION = 10000; // Reduced to 10 seconds for better sync

  // Loading lock to prevent multiple simultaneous loads
  private static loadingLock = new Map<string, Promise<Trade[]>>();

  // Save lock to prevent multiple simultaneous saves
  private static savingLock = new Map<string, Promise<boolean>>();

  // Smart cache warming - only loads if cache is empty or stale
  static async warmTradesCache(): Promise<Trade[]> {
    try {
      const userId = await AuthService.getUserId();
      if (!userId) return [];

      const cacheKey = `trades_${userId}`;
      const cached = this.tradesCache.get(cacheKey);
      const now = Date.now();

      // Check cache validity
      if (cached && (now - cached.timestamp) < this.CACHE_DURATION) {
        console.log('📋 Cache is fresh, skipping warm');
        return cached.data;
      }

      console.log('🔥 Cache warming: loading fresh trades data');
      return await this.getAllTrades();
    } catch (error) {
      console.error('❌ Failed to warm trades cache:', error);
      return [];
    }
  }

  static async getAllTrades(): Promise<Trade[]> {
    const startTime = performance.now();

    try {
      // Ensure session is initialized before any cache operations
      this.ensureSessionInitialized();

      const userId = await AuthService.getUserId()
      if (!userId) {
        // User not authenticated - return empty array silently for guest mode
        return []
      }

      // CRITICAL FIX: Prevent multiple simultaneous loads
      const lockKey = `loading_${userId}`;
      if (this.loadingLock.has(lockKey)) {
        // Reduced logging frequency to prevent console spam
        if (Math.random() < 0.1) { // Only log 10% of the time
          console.log('⏳ Waiting for existing load to complete...');
        }
        return await this.loadingLock.get(lockKey)!;
      }

      // Create loading promise and store it
      const loadingPromise = this.performActualLoad(userId, startTime);
      this.loadingLock.set(lockKey, loadingPromise);

      try {
        const result = await loadingPromise;
        // Return trades as-is (no deduplication)
        console.log(`🔍 MAIN LOAD: Returning ${result.length} trades from database`);
        return result;
      } finally {
        // Always clean up the lock
        this.loadingLock.delete(lockKey);
      }
    } catch (error) {
      console.error('❌ Failed to get trades from Supabase:', error)
      return []
    }
  }

  private static async performActualLoad(userId: string, startTime: number): Promise<Trade[]> {
    try {
      // PERFORMANCE: Re-enable cache with proper invalidation
      const cacheKey = `trades_${userId}`;
      const cached = this.tradesCache.get(cacheKey);
      const now = Date.now();

      // Use cache only if valid and fresh
      if (cached && (now - cached.timestamp) < this.CACHE_DURATION) {
        console.log(`📋 Using cached trades data (age: ${Math.round((now - cached.timestamp) / 1000)}s)`);
        // Return cached data as-is (no deduplication)
        return cached.data;
      }

      console.log('🔄 Loading fresh data from Supabase');

      // Only check count for debugging if cache miss
      const { count, error: countError } = await supabase
        .from('trades')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (!countError) {
        console.log(`📊 Database contains ${count} trades for user ${userId}`);
      }

      // Complete query with all required fields matching database schema
      const { data, error } = await supabase
        .from('trades')
        .select(`
          id, user_id, trade_no, name, date, entry, avg_entry, sl, tsl, buy_sell, cmp,
          setup, base_duration, initial_qty,
          pyramid1_price, pyramid1_qty, pyramid1_date,
          pyramid2_price, pyramid2_qty, pyramid2_date,
          position_size, allocation, sl_percent,
          exit1_price, exit1_qty, exit1_date,
          exit2_price, exit2_qty, exit2_date,
          exit3_price, exit3_qty, exit3_date,
          open_qty, exited_qty, avg_exit_price, stock_move, reward_risk, holding_days,
          position_status, realised_amount, pl_rs, pf_impact, cumm_pf,
          plan_followed, exit_trigger, proficiency_growth_areas, sector, open_heat,
          notes, chart_attachments, user_edited_fields, cmp_auto_fetched, needs_recalculation,
          created_at, updated_at
        `)
        .eq('user_id', userId)
        .order('trade_no', { ascending: true })

      if (error) throw error

      const trades = data.map(dbRowToTrade);

      // Return trades as-is (no deduplication)

      // FULL FIX: Store cache with hash for smart validation
      const { data: hashData } = await supabase
        .from('trades')
        .select('id, updated_at, created_at')
        .eq('user_id', userId)
        .order('trade_no', { ascending: true });

      const dataHash = hashData ? hashData.map(item => `${item.id}-${item.updated_at || item.created_at}`).join('|') : '';

      this.tradesCache.set(cacheKey, {
        data: trades,
        timestamp: Date.now(),
        hash: dataHash
      });

      const endTime = performance.now();
      console.log(`⚡ Trades loaded from Supabase in ${Math.round(endTime - startTime)}ms`);
      console.log(`📊 Total trades loaded: ${trades.length}`);

      return trades;
    } catch (error) {
      console.error('❌ Failed to perform actual load from Supabase:', error)
      return []
    }
  }

  // Clear cache when trades are updated
  static clearTradesCache(userId?: string): void {
    if (userId) {
      this.tradesCache.delete(`trades_${userId}`);
    } else {
      this.tradesCache.clear();
    }
  }

  // Removed automatic session ID generation - sessions are managed without automatic numbering
  static startNewSession(): void {
    // Only clear caches if they exist and are initialized
    try {
      this.clearAllCaches();
    } catch (error) {
      console.warn('⚠️ Could not clear caches during session start:', error);
    }

    this.sessionInitialized = true;
  }

  // Initialize session on first method call (lazy initialization)
  private static sessionInitialized = false;

  private static ensureSessionInitialized(): void {
    if (!this.sessionInitialized) {
      this.sessionInitialized = true;
    }
  }

  // Removed deduplication logic - users control their own data

  // Smart cache update for single trade operations
  static updateTradeInCache(trade: Trade, userId: string): void {
    const cacheKey = `trades_${userId}`;
    const cached = this.tradesCache.get(cacheKey);

    if (cached) {
      // Validate trade ID before updating cache
      if (!trade.id || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(trade.id)) {
        console.warn(`⚠️ Skipping cache update for trade with invalid ID: ${trade.id}`);
        return;
      }

      // Simple cache update - find by ID or add new
      const existingIndex = cached.data.findIndex(t => t.id === trade.id);

      if (existingIndex >= 0) {
        // Update existing trade by ID
        cached.data[existingIndex] = trade;
        console.log(`🔄 Updated trade in cache: ${trade.name}`);
      } else {
        // Add new trade
        cached.data.push(trade);
        console.log(`➕ Added new trade to cache: ${trade.name}`);
      }

      // Update timestamp
      cached.timestamp = Date.now();
    } else {
      console.log(`📋 No cache to update for user ${userId}`);
    }
  }

  // Smart cache removal for deleted trades
  static removeTradeFromCache(tradeId: string, userId: string): void {
    const cacheKey = `trades_${userId}`;
    const cached = this.tradesCache.get(cacheKey);

    if (cached) {
      // Validate trade ID before removing from cache
      if (!tradeId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(tradeId)) {
        console.warn(`⚠️ Skipping cache removal for invalid trade ID: ${tradeId}`);
        return;
      }

      const originalLength = cached.data.length;
      cached.data = cached.data.filter(t => t.id !== tradeId);
      if (cached.data.length < originalLength) {
        console.log(`🗑️ Removed trade from cache: ${tradeId}`);
        cached.timestamp = Date.now();
      } else {
        console.log(`🔍 Trade not found in cache for removal: ${tradeId}`);
      }
    }
  }

  static async getTrade(id: string): Promise<Trade | null> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) {
        // User not authenticated - return null silently for guest mode
        return null
      }

      // Use ID as-is for lookup
      const { data, error } = await supabase
        .from('trades')
        .select('*')
        .eq('id', id)
        .eq('user_id', userId)
        .single()

      if (error) {
        // Don't log as error if trade simply doesn't exist (expected for temporary trades)
        if (error.code === 'PGRST116') {
          console.log('📝 Trade not found in Supabase (may be temporary):', id);
          return null;
        }
        throw error;
      }

      return data ? dbRowToTrade(data) : null
    } catch (error) {
      // Only log unexpected errors
      if (error.code !== 'PGRST116') {
        console.error('❌ Failed to get trade from Supabase:', error)
      }
      return null
    }
  }

  /**
   * Get trade directly from Supabase only (no local fallback)
   * Used for verifying trade exists in Supabase for foreign key constraints
   */
  static async getTradeFromSupabaseOnly(id: string): Promise<Trade | null> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) {
        // User not authenticated - return null silently for guest mode
        return null
      }

      const { data, error } = await supabase
        .from('trades')
        .select('*')
        .eq('id', id)
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') throw error

      return data ? dbRowToTrade(data) : null
    } catch (error) {
      console.error('❌ Failed to get trade from Supabase only:', error)
      return null
    }
  }

  static async saveTrade(trade: Trade): Promise<boolean> {
    // OPTIMIZED: Minimal logging for production performance
    console.log(`💾 Saving: ${trade.name || 'Unnamed'} (#${trade.tradeNo})`);

    try {
      const userId = await AuthService.getUserId()
      if (!userId) {
        console.warn('⚠️ Cannot save trade - user not authenticated')
        return false
      }

      // OPTIMIZED: Quick validation without excessive logging
      const validation = validateTradeForDatabase(trade)
      if (!validation.isValid) {
        console.warn('⚠️ Validation failed, sanitizing:', validation.errors)
        trade = sanitizeTradeForDatabase(trade)
      }

      const dbRow = tradeToDbRow(trade, userId)
      const uuid = dbRow.id

      // OPTIMIZED: Skip duplicate check for existing trades (performance improvement)
      // Only check for duplicates when creating new trades
      const isNewTrade = !trade.id || trade.id.length < 10;

      // Removed duplicate trade number checking - users control their own numbering system

      // OPTIMIZED: Direct upsert instead of check-then-update/insert
      const { error } = await supabase
        .from('trades')
        .upsert(dbRow, {
          onConflict: 'id',
          ignoreDuplicates: false
        })

      if (error) {
        console.error('❌ Error saving trade:', error)
        throw error
      }

      // OPTIMIZED: Minimal cache operations
      this.updateTradeInCache(trade, userId);
      // Only invalidate analytics, keep trade cache fresh
      this.invalidateAnalyticsCache(userId);

      console.log(`✅ Saved: ${trade.name || 'Unnamed'}`);
      return true

    } catch (error) {
      console.error(`❌ Save failed for ${trade.name || 'Unnamed'}:`, error)
      return false
    }
  }

  static async saveAllTrades(trades: Trade[], confirmDestruction: boolean = false): Promise<boolean> {
    // CRITICAL SECURITY FIX: Completely disable this destructive operation
    console.error('❌ SECURITY BLOCK: saveAllTrades is permanently disabled for safety');
    console.error('❌ This operation has been removed to prevent accidental data loss');
    console.error('❌ Use SupabaseService.saveTrade() for individual trades');
    console.error('❌ Use SupabaseService.bulkImportTrades() for importing new trades');
    console.error('❌ Use incremental save operations for multiple trade updates');

    throw new Error('saveAllTrades is permanently disabled - use safe alternatives instead');
  }

  // New method for bulk import that appends trades instead of replacing
  static async bulkImportTrades(trades: Trade[]): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) {
        console.warn('⚠️ Cannot import trades - user not authenticated')
        return false
      }

      console.log(`📥 Importing ${trades.length} new trades to Supabase for user:`, userId)

      if (trades.length === 0) {
        console.log('ℹ️ No trades to import')
        return true
      }

      // Validate and sanitize all trades before saving
      console.log('🔍 Validating trade data for database constraints...')
      const validation = validateTradesBatch(trades)

      if (!validation.isValid) {
        console.warn('⚠️ Some trades have validation issues:', validation.errors)
        console.log('🔧 Sanitizing trades to fit database constraints')
        trades = trades.map(trade => sanitizeTradeForDatabase(trade))
      }

      // Convert trades to database rows (UUIDs generated by tradeToDbRow function)
      const dbRows = trades.map((trade, index) => {
        const dbRow = tradeToDbRow(trade, userId)
        return dbRow
      })

      // Insert new trades in batches
      const batchSize = 25
      const totalBatches = Math.ceil(dbRows.length / batchSize)
      console.log(`📦 Inserting ${dbRows.length} trades in ${totalBatches} batches of ${batchSize}`)

      for (let i = 0; i < totalBatches; i++) {
        const batch = dbRows.slice(i * batchSize, (i + 1) * batchSize)
        console.log(`📦 Inserting batch ${i + 1}/${totalBatches} (${batch.length} trades)`)

        const { error } = await supabase
          .from('trades')
          .insert(batch)

        if (error) {
          console.error(`❌ Failed to insert batch ${i + 1}:`, error)
          throw error
        }

        console.log(`✅ Successfully inserted batch ${i + 1}/${totalBatches}`)
      }

      // CRITICAL FIX: Clear cache but preserve monthly performance cache for bulk imports
      this.clearTradesCache(userId)
      this.invalidateTradeRelatedCachesExceptMonthlyPerformance(userId)
      console.log('🗑️ Trade caches invalidated after bulk import (preserved monthly performance cache)')

      console.log(`✅ Successfully imported ${trades.length} trades`)
      return true
    } catch (error) {
      console.error('❌ Failed to bulk import trades to Supabase:', error)
      return false
    }
  }

  private static async performActualSave(trades: Trade[], userId: string): Promise<boolean> {
    // CRITICAL SECURITY FIX: Disable this destructive method
    console.error('❌ SECURITY BLOCK: performActualSave is permanently disabled');
    console.error('❌ This destructive operation has been removed for safety');
    throw new Error('performActualSave is permanently disabled - use safe alternatives instead');
  }

  static async deleteTrade(id: string): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      // Use ID as-is for deletion
      const { error } = await supabase
        .from('trades')
        .delete()
        .eq('id', id)
        .eq('user_id', userId)

      if (error) throw error

      // Removed automatic ID mapping logic

      // SMART CACHE: Remove trade from cache instead of clearing
      this.removeTradeFromCache(id, userId);
      // Only invalidate analytics caches, keep trades cache
      this.invalidateTradeRelatedCaches(userId);
      console.log('🔄 Trade removed from cache, analytics caches invalidated');

      return true
    } catch (error) {
      console.error('❌ Failed to delete trade from Supabase:', error)
      return false
    }
  }

  // ===== USER PREFERENCES =====
  
  static async getUserPreferences(): Promise<any | null> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') throw error // PGRST116 = no rows returned

      return data || null
    } catch (error) {
      console.error('❌ Failed to get user preferences from Supabase:', error)
      return null
    }
  }

  // CRITICAL FIX: Add debouncing to prevent 47,407 user preference saves
  private static userPreferencesSaveTimeout: NodeJS.Timeout | null = null;
  private static pendingUserPreferences: any = null;

  static async saveUserPreferences(preferences: any): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      // Store pending preferences
      this.pendingUserPreferences = { ...preferences, user_id: userId };

      // Clear existing timeout
      if (this.userPreferencesSaveTimeout) {
        clearTimeout(this.userPreferencesSaveTimeout);
      }

      // Set new timeout with 2 second debounce
      this.userPreferencesSaveTimeout = setTimeout(async () => {
        try {
          const { error } = await supabase
            .from('user_preferences')
            .upsert(this.pendingUserPreferences, {
              onConflict: 'user_id'
            })

          if (error) throw error
          console.log('✅ User preferences saved (debounced)');
        } catch (error) {
          console.error('❌ Failed to save user preferences to Supabase:', error)
        } finally {
          this.pendingUserPreferences = null;
          this.userPreferencesSaveTimeout = null;
        }
      }, 2000); // 2 second debounce

      return true
    } catch (error) {
      console.error('❌ Failed to save user preferences to Supabase:', error)
      return false
    }
  }

  // CRITICAL FIX: Immediate save for important preferences (bypasses debouncing)
  static async saveUserPreferencesImmediate(preferences: any): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      // Get existing preferences
      const existing = await this.getUserPreferences() || {};
      const updated = { ...existing, ...preferences, user_id: userId };

      const { error } = await supabase
        .from('user_preferences')
        .upsert(updated, {
          onConflict: 'user_id'
        })

      if (error) throw error
      return true
    } catch (error) {
      console.error('❌ Failed to save user preferences immediately:', error)
      return false
    }
  }

  // ===== PORTFOLIO DATA =====
  
  static async getPortfolioData(): Promise<any[]> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('portfolio_data')
        .select('*')
        .eq('user_id', userId)

      if (error) throw error

      return data || []
    } catch (error) {
      console.error('❌ Failed to get portfolio data from Supabase:', error)
      return []
    }
  }

  static async savePortfolioData(data: any[]): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      // Delete existing portfolio data
      const { error: deleteError } = await supabase
        .from('portfolio_data')
        .delete()
        .eq('user_id', userId)

      if (deleteError) throw deleteError

      // Insert new portfolio data
      const dataWithUserId = data.map(item => ({ ...item, user_id: userId }))

      const { error: insertError } = await supabase
        .from('portfolio_data')
        .insert(dataWithUserId)

      if (insertError) throw insertError

      return true
    } catch (error) {
      console.error('❌ Failed to save portfolio data to Supabase:', error)
      return false
    }
  }

  // Specific portfolio data functions
  static async getYearlyStartingCapitals(): Promise<any[]> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) return []

      const { data, error } = await supabase
        .from('portfolio_data')
        .select('*')
        .eq('user_id', userId)
        .eq('type', 'yearly_capital')

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('❌ Failed to get yearly starting capitals:', error)
      return []
    }
  }

  static async saveYearlyStartingCapitals(capitals: any[]): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) return false

      // CRITICAL FIX: Use UPSERT instead of DELETE + INSERT to prevent data loss
      // This prevents historical data deletion

      for (const capital of capitals) {
        const { error: upsertError } = await supabase
          .from('portfolio_data')
          .upsert({
            // CRITICAL FIX: Generate UUID for yearly capitals if not present
            id: capital.id || uuidv4(),
            user_id: userId,
            type: 'yearly_capital',
            year: capital.year,
            amount: capital.startingCapital,
            updated_at: new Date().toISOString()
          }, {
            // CRITICAL FIX: Use 'id' as the conflict resolution since we're providing it
            onConflict: 'id'
          })

        if (upsertError) {
          console.error('❌ Failed to upsert yearly capital for year', capital.year, ':', upsertError)
          // Continue with other years instead of failing completely
        }
      }

      return true
    } catch (error) {
      console.error('❌ Failed to save yearly starting capitals:', error)
      return false
    }
  }

  static async getCapitalChanges(): Promise<any[]> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) return []

      const { data, error } = await supabase
        .from('portfolio_data')
        .select('*')
        .eq('user_id', userId)
        .eq('type', 'capital_change')

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('❌ Failed to get capital changes:', error)
      return []
    }
  }

  static async saveCapitalChanges(changes: any[]): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) return false

      // CRITICAL FIX: Use UPSERT instead of DELETE + INSERT to prevent data loss
      // This prevents historical data deletion

      for (const change of changes) {
        const { error: upsertError } = await supabase
          .from('portfolio_data')
          .upsert({
            id: change.id, // Use existing ID if available
            user_id: userId,
            type: 'capital_change',
            date: change.date,
            amount: change.amount,
            description: change.description || change.type,
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'id'
          })

        if (upsertError) {
          console.error('❌ Failed to upsert capital change:', change.id, ':', upsertError)
          // Continue with other changes instead of failing completely
        }
      }

      return true
    } catch (error) {
      console.error('❌ Failed to save capital changes:', error)
      return false
    }
  }

  static async getMonthlyStartingCapitalOverrides(): Promise<any[]> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) return []

      const { data, error } = await supabase
        .from('portfolio_data')
        .select('*')
        .eq('user_id', userId)
        .eq('type', 'monthly_override')

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('❌ Failed to get monthly overrides:', error)
      return []
    }
  }

  static async saveMonthlyStartingCapitalOverrides(overrides: any[]): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) return false

      // CRITICAL FIX: Use UPSERT instead of DELETE + INSERT to prevent data loss
      // This prevents historical data deletion

      for (const override of overrides) {
        const { error: upsertError } = await supabase
          .from('portfolio_data')
          .upsert({
            // CRITICAL FIX: Generate UUID for monthly overrides if not present
            id: override.id || uuidv4(),
            user_id: userId,
            type: 'monthly_override',
            year: override.year,
            month: override.month,
            amount: override.startingCapital,
            updated_at: new Date().toISOString()
          }, {
            // CRITICAL FIX: Use 'id' as the conflict resolution since we're providing it
            onConflict: 'id'
          })

        if (upsertError) {
          console.error('❌ Failed to upsert monthly override for', override.month, override.year, ':', upsertError)
          // Continue with other overrides instead of failing completely
        }
      }

      return true
    } catch (error) {
      console.error('❌ Failed to save monthly overrides:', error)
      return false
    }
  }

  static async getMonthlyPortfolioSizes(): Promise<any[]> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) return []

      const { data, error } = await supabase
        .from('portfolio_data')
        .select('*')
        .eq('user_id', userId)
        .eq('type', 'monthly_size')

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('❌ Failed to get monthly portfolio sizes:', error)
      return []
    }
  }

  static async saveMonthlyPortfolioSizes(sizes: any[]): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) return false

      // Delete existing monthly sizes
      const { error: deleteError } = await supabase
        .from('portfolio_data')
        .delete()
        .eq('user_id', userId)
        .eq('type', 'monthly_size')

      if (deleteError) throw deleteError

      // Insert new monthly sizes
      const dataWithUserId = sizes.map(size => ({
        user_id: userId,
        type: 'monthly_size',
        year: size.year,
        month: size.month,
        amount: size.size,
        updated_at: size.updatedAt || new Date().toISOString()
      }))

      if (dataWithUserId.length > 0) {
        const { error: insertError } = await supabase
          .from('portfolio_data')
          .insert(dataWithUserId)

        if (insertError) throw insertError
      }

      return true
    } catch (error) {
      console.error('❌ Failed to save monthly portfolio sizes:', error)
      return false
    }
  }

  // ===== TRADE SETTINGS =====

  static async getTradeSettings(): Promise<any | null> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) {
        // User not authenticated - return null silently for guest mode
        return null
      }

      const { data, error } = await supabase
        .from('trade_settings')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') throw error

      return data || null
    } catch (error) {
      console.error('❌ Failed to get trade settings from Supabase:', error)
      return null
    }
  }

  static async saveTradeSettings(settings: any): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) {
        // User not authenticated - return false silently for guest mode
        return false
      }

      const { error } = await supabase
        .from('trade_settings')
        .upsert({
          ...settings,
          user_id: userId
        }, {
          onConflict: 'user_id'
        })

      if (error) throw error

      return true
    } catch (error) {
      console.error('❌ Failed to save trade settings to Supabase:', error)
      return false
    }
  }

  // ===== TAX DATA =====

  static async getTaxData(year: number): Promise<any | null> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('tax_data')
        .select('*')
        .eq('user_id', userId)
        .eq('year', year)
        .single()

      if (error && error.code !== 'PGRST116') throw error

      return data || null
    } catch (error) {
      console.error('❌ Failed to get tax data from Supabase:', error)
      return null
    }
  }

  static async saveTaxData(year: number, data: any): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('tax_data')
        .upsert({
          user_id: userId,
          year,
          data
        }, {
          onConflict: 'user_id,year'
        })

      if (error) throw error


      return true
    } catch (error) {
      console.error('❌ Failed to save tax data to Supabase:', error)
      return false
    }
  }

  // ===== MILESTONES DATA REMOVED =====
  // All milestone functionality has been completely removed from the application

  // ===== MISC DATA =====

  // CRITICAL FIX: Enhanced caching for misc data to prevent 478,109 excessive queries
  private static miscDataCache = new Map<string, { data: any; timestamp: number; userId: string }>();
  private static MISC_CACHE_DURATION = 30 * 60 * 1000; // Increased to 30 minutes cache
  private static miscDataLoadingLock = new Map<string, Promise<any>>();

  static async getMiscData(key: string): Promise<any> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) {
        // User not authenticated - return null silently for guest mode
        return null
      }

      // PERFORMANCE: Check cache first
      const cacheKey = `${userId}_${key}`;
      const cached = this.miscDataCache.get(cacheKey);
      const now = Date.now();

      if (cached && (now - cached.timestamp) < this.MISC_CACHE_DURATION && cached.userId === userId) {
        // Silent cache hit - no logging to reduce console noise
        return cached.data;
      }

      // CRITICAL FIX: Prevent duplicate simultaneous requests
      const requestKey = `${userId}_${key}`;
      if (this.miscDataLoadingLock.has(requestKey)) {
        // Reduced logging to prevent console spam
        if (!key.startsWith('misc_custom_')) {
          console.log(`⏳ Waiting for existing misc data request: ${key}`);
        }
        return await this.miscDataLoadingLock.get(requestKey);
      }

      // Only log for non-custom keys and limit frequency
      const shouldLog = !key.startsWith('misc_custom_') && (!cached || (now - cached.timestamp) > 30000);
      if (shouldLog) {
        console.log('🔍 Getting misc data for key:', key);
      }

      // CRITICAL FIX: Create loading promise and store it
      const loadingPromise = (async () => {
        try {
          const { data, error } = await supabase
            .from('misc_data')
            .select('value')
            .eq('user_id', userId)
            .eq('key', key)
            .maybeSingle() // Use maybeSingle instead of single to avoid errors when no data exists

          if (error) {
            console.error('❌ Error getting misc data:', error)
            throw error
          }

          const result = data?.value || null;

          // PERFORMANCE: Cache the result
          this.miscDataCache.set(cacheKey, {
            data: result,
            timestamp: Date.now(),
            userId
          });

          // Only log successful fetches for important keys
          if (shouldLog) {
            console.log('✅ Got misc data:', result || 'null')
          }

          return result;
        } finally {
          // Always clean up the loading lock
          this.miscDataLoadingLock.delete(requestKey);
        }
      })();

      // Store the loading promise
      this.miscDataLoadingLock.set(requestKey, loadingPromise);

      return await loadingPromise;

      return result;
    } catch (error) {
      console.error('❌ Failed to get misc data from Supabase:', error)
      return null
    }
  }

  // CRITICAL FIX: Add debouncing to prevent excessive misc data saves
  private static miscDataSaveTimeouts = new Map<string, NodeJS.Timeout>();
  private static pendingMiscData = new Map<string, { key: string; value: any; userId: string }>();

  static async saveMiscData(key: string, value: any): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) {
        // User not authenticated - return false silently for guest mode
        return false
      }

      const timeoutKey = `${userId}_${key}`;

      // Store pending data
      this.pendingMiscData.set(timeoutKey, { key, value, userId });

      // Clear existing timeout
      if (this.miscDataSaveTimeouts.has(timeoutKey)) {
        clearTimeout(this.miscDataSaveTimeouts.get(timeoutKey)!);
      }

      // Set new timeout with 2 second debounce
      const timeout = setTimeout(async () => {
        try {
          const pending = this.pendingMiscData.get(timeoutKey);
          if (!pending) return;

          const { error } = await supabase
            .from('misc_data')
            .upsert({
              user_id: pending.userId,
              key: pending.key,
              value: pending.value
            }, {
              onConflict: 'user_id,key'
            })

          if (error) throw error

          // PERFORMANCE: Update cache immediately to prevent unnecessary refetch
          const cacheKey = `${pending.userId}_${pending.key}`;
          this.miscDataCache.set(cacheKey, {
            data: pending.value,
            timestamp: Date.now(),
            userId: pending.userId
          });

          console.log(`✅ Saved misc data to Supabase (debounced): ${pending.key}`);
        } catch (error) {
          console.error('❌ Failed to save misc data to Supabase:', error)
        } finally {
          this.pendingMiscData.delete(timeoutKey);
          this.miscDataSaveTimeouts.delete(timeoutKey);
        }
      }, 2000); // 2 second debounce

      this.miscDataSaveTimeouts.set(timeoutKey, timeout);

      return true
    } catch (error) {
      console.error('❌ Failed to save misc data to Supabase:', error)
      return false
    }
  }

  // Alias for backward compatibility
  static async setMiscData(key: string, value: any): Promise<boolean> {
    return this.saveMiscData(key, value);
  }

  // PERFORMANCE: Clear misc data cache for specific key or all
  static clearMiscDataCache(key?: string, userId?: string): void {
    if (key && userId) {
      const cacheKey = `${userId}_${key}`;
      this.miscDataCache.delete(cacheKey);
    } else {
      this.miscDataCache.clear();
    }
  }

  // PERFORMANCE: Specialized cache stores for different data types
  private static monthlyPerformanceCache = new Map<string, { data: any; timestamp: number; userId: string }>();
  private static chartViewerCache = new Map<string, { data: any; timestamp: number; userId: string }>();
  private static drawdownCache = new Map<string, { data: any; timestamp: number; userId: string }>();
  private static analyticsCache = new Map<string, { data: any; timestamp: number; userId: string }>();

  // Cache durations for different data types
  private static MONTHLY_PERFORMANCE_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private static CHART_VIEWER_CACHE_DURATION = 10 * 60 * 1000; // 10 minutes
  private static DRAWDOWN_CACHE_DURATION = 3 * 60 * 1000; // 3 minutes
  private static ANALYTICS_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // PERFORMANCE: Global cache management with safety checks
  static clearAllCaches(): void {
    try {
      // Clear caches safely with existence checks
      if (this.tradesCache) this.tradesCache.clear();
      if (this.miscDataCache) this.miscDataCache.clear();
      if (this.monthlyPerformanceCache) this.monthlyPerformanceCache.clear();
      if (this.chartViewerCache) this.chartViewerCache.clear();
      if (this.drawdownCache) this.drawdownCache.clear();
      if (this.analyticsCache) this.analyticsCache.clear();
      console.log('🗑️ All caches cleared');
    } catch (error) {
      console.error('❌ Error clearing caches:', error);
    }
  }

  static getCacheStats(): {
    tradesCache: number;
    miscDataCache: number;
    monthlyPerformanceCache: number;
    chartViewerCache: number;
    drawdownCache: number;
    analyticsCache: number;
  } {
    return {
      tradesCache: this.tradesCache.size,
      miscDataCache: this.miscDataCache.size,
      monthlyPerformanceCache: this.monthlyPerformanceCache.size,
      chartViewerCache: this.chartViewerCache.size,
      drawdownCache: this.drawdownCache.size,
      analyticsCache: this.analyticsCache.size
    };
  }

  // PERFORMANCE: Cache preloading system
  private static preloadingInProgress = false;
  private static preloadPromise: Promise<void> | null = null;

  /**
   * Preload frequently accessed data into cache
   * Call this on app startup for better performance
   */
  static async preloadCache(): Promise<void> {
    // Prevent multiple simultaneous preloading
    if (this.preloadingInProgress) {
      return this.preloadPromise || Promise.resolve();
    }

    this.preloadingInProgress = true;
    console.log('🚀 Starting cache preloading...');
    const startTime = performance.now();

    this.preloadPromise = this.performCachePreload();

    try {
      await this.preloadPromise;
      const endTime = performance.now();
      console.log(`⚡ Cache preloading completed in ${Math.round(endTime - startTime)}ms`);
    } finally {
      this.preloadingInProgress = false;
      this.preloadPromise = null;
    }
  }

  private static async performCachePreload(): Promise<void> {
    try {
      const userId = await AuthService.getUserId();
      if (!userId) {
        console.log('👤 User not authenticated - skipping cache preload');
        return;
      }

      // CRITICAL FIX: Reduce preloading to prevent login slowdown
      const criticalTasks = [
        // Only preload essential data during login
        this.preloadTrades(userId),
        this.preloadCriticalMiscData(userId)
      ];

      await Promise.allSettled(criticalTasks);
      console.log('📦 Critical cache preloading tasks completed');

      // CRITICAL FIX: Delay non-critical preloading to after login
      setTimeout(() => {
        console.log('📦 Starting delayed preloading...');
        Promise.allSettled([
          this.preloadUserPreferences(userId),
          this.preloadPortfolioData(userId)
        ]).catch(error => console.warn('Delayed preloading failed:', error));
      }, 2000); // 2 second delay
    } catch (error) {
      console.error('❌ Cache preloading failed:', error);
    }
  }

  // Individual preloading methods
  private static async preloadTrades(userId: string): Promise<void> {
    try {
      console.log('📊 Preloading trades...');
      await this.getAllTrades(); // This will cache the trades
      console.log('✅ Trades preloaded');
    } catch (error) {
      console.error('❌ Failed to preload trades:', error);
    }
  }

  private static async preloadCriticalMiscData(userId: string): Promise<void> {
    try {
      console.log('⚙️ Preloading critical misc data...');

      // Preload most frequently accessed misc data
      const criticalKeys = [
        'accountingMethod',
        'globalFilter',
        'taxData',
        'dashboardConfig',
        'userPreferences'
      ];

      await Promise.allSettled(
        criticalKeys.map(key => this.getMiscData(key))
      );

      console.log('✅ Critical misc data preloaded');
    } catch (error) {
      console.error('❌ Failed to preload critical misc data:', error);
    }
  }

  private static async preloadUserPreferences(userId: string): Promise<void> {
    try {
      console.log('👤 Preloading user preferences...');
      await this.getUserPreferences();
      console.log('✅ User preferences preloaded');
    } catch (error) {
      console.error('❌ Failed to preload user preferences:', error);
    }
  }

  private static async preloadPortfolioData(userId: string): Promise<void> {
    try {
      console.log('💰 Preloading portfolio data...');

      // Preload current year portfolio data
      const currentYear = new Date().getFullYear();
      await Promise.allSettled([
        this.getPortfolioData(currentYear),
        this.getCapitalChanges(currentYear)
        // REMOVED: getMilestonesData() - milestone functionality removed
      ]);

      console.log('✅ Portfolio data preloaded');
    } catch (error) {
      console.error('❌ Failed to preload portfolio data:', error);
    }
  }

  // PERFORMANCE: Monthly Performance Cache Methods
  static async getMonthlyPerformanceData(year: number, accountingMethod: string): Promise<any> {
    try {
      const userId = await AuthService.getUserId();
      if (!userId) return null;

      const cacheKey = `${userId}_monthly_${year}_${accountingMethod}`;
      const cached = this.monthlyPerformanceCache.get(cacheKey);
      const now = Date.now();

      if (cached && (now - cached.timestamp) < this.MONTHLY_PERFORMANCE_CACHE_DURATION) {
        console.log('📊 Using cached monthly performance data');
        return cached.data;
      }

      console.log('🔄 Computing fresh monthly performance data');

      // This would typically involve complex calculations
      // For now, we'll cache the computation trigger
      const data = {
        year,
        accountingMethod,
        timestamp: now,
        // Placeholder for actual monthly performance calculations
        computed: true
      };

      this.monthlyPerformanceCache.set(cacheKey, {
        data,
        timestamp: now,
        userId
      });

      return data;
    } catch (error) {
      console.error('❌ Failed to get monthly performance data:', error);
      return null;
    }
  }

  // PERFORMANCE: Chart Viewer Cache Methods
  static async getChartViewerData(filters?: any): Promise<any> {
    try {
      const userId = await AuthService.getUserId();
      if (!userId) return null;

      const filterKey = filters ? JSON.stringify(filters) : 'all';
      const cacheKey = `${userId}_charts_${filterKey}`;
      const cached = this.chartViewerCache.get(cacheKey);
      const now = Date.now();

      if (cached && (now - cached.timestamp) < this.CHART_VIEWER_CACHE_DURATION) {
        console.log('🖼️ Using cached chart viewer data');
        return cached.data;
      }

      console.log('🔄 Loading fresh chart viewer data');

      // Get all trades and extract chart data
      const trades = await this.getAllTrades();
      const chartData = trades
        .filter(trade => trade.chartAttachments && trade.chartAttachments.length > 0)
        .map(trade => ({
          tradeId: trade.id,
          tradeName: trade.name,
          charts: trade.chartAttachments,
          outcome: trade.outcome,
          setup: trade.setup,
          date: trade.entryDate
        }));

      this.chartViewerCache.set(cacheKey, {
        data: chartData,
        timestamp: now,
        userId
      });

      return chartData;
    } catch (error) {
      console.error('❌ Failed to get chart viewer data:', error);
      return null;
    }
  }

  // PERFORMANCE: Get recent chart images for preloading
  static async getRecentChartImages(limit: number = 10): Promise<any[]> {
    try {
      const userId = await AuthService.getUserId();
      if (!userId) return [];

      console.log(`🖼️ Loading recent ${limit} chart images`);

      // Get recent chart image blobs
      const { data, error } = await supabase
        .from('chart_image_blobs')
        .select('id, trade_id, filename, image_type, uploaded_at, size_bytes')
        .eq('user_id', userId)
        .order('uploaded_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('❌ Failed to get recent chart images:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('❌ Failed to get recent chart images:', error);
      return [];
    }
  }

  // PERFORMANCE: Get chart statistics for caching
  static async getChartStatistics(): Promise<any> {
    try {
      const userId = await AuthService.getUserId();
      if (!userId) return null;

      const cacheKey = `${userId}_chart_stats`;
      const cached = this.chartViewerCache.get(cacheKey);
      const now = Date.now();

      if (cached && (now - cached.timestamp) < this.CHART_VIEWER_CACHE_DURATION) {
        console.log('📊 Using cached chart statistics');
        return cached.data;
      }

      console.log('🔄 Computing chart statistics');

      // Get chart count and basic stats
      const { count, error } = await supabase
        .from('chart_image_blobs')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (error) {
        console.error('❌ Failed to get chart statistics:', error);
        return null;
      }

      const stats = {
        totalCharts: count || 0,
        timestamp: now
      };

      this.chartViewerCache.set(cacheKey, {
        data: stats,
        timestamp: now,
        userId
      });

      return stats;
    } catch (error) {
      console.error('❌ Failed to get chart statistics:', error);
      return null;
    }
  }

  // PERFORMANCE: Drawdown Cache Methods
  static async getDrawdownData(accountingMethod: string, dateRange?: any): Promise<any> {
    try {
      const userId = await AuthService.getUserId();
      if (!userId) return null;

      const rangeKey = dateRange ? `${dateRange.start}_${dateRange.end}` : 'all';
      const cacheKey = `${userId}_drawdown_${accountingMethod}_${rangeKey}`;
      const cached = this.drawdownCache.get(cacheKey);
      const now = Date.now();

      if (cached && (now - cached.timestamp) < this.DRAWDOWN_CACHE_DURATION) {
        console.log('📉 Using cached drawdown data');
        return cached.data;
      }

      console.log('🔄 Computing fresh drawdown data');

      // Get trades for drawdown calculation
      const trades = await this.getAllTrades();

      // Placeholder for actual drawdown calculations
      const drawdownData = {
        accountingMethod,
        dateRange,
        maxDrawdown: 0,
        currentDrawdown: 0,
        drawdownPeriods: [],
        timestamp: now,
        tradesCount: trades.length
      };

      this.drawdownCache.set(cacheKey, {
        data: drawdownData,
        timestamp: now,
        userId
      });

      return drawdownData;
    } catch (error) {
      console.error('❌ Failed to get drawdown data:', error);
      return null;
    }
  }

  // PERFORMANCE: Analytics Cache Methods
  static async getAnalyticsData(type: 'performance' | 'risk' | 'metrics', params?: any): Promise<any> {
    try {
      const userId = await AuthService.getUserId();
      if (!userId) return null;

      const paramsKey = params ? JSON.stringify(params) : 'default';
      const cacheKey = `${userId}_analytics_${type}_${paramsKey}`;
      const cached = this.analyticsCache.get(cacheKey);
      const now = Date.now();

      if (cached && (now - cached.timestamp) < this.ANALYTICS_CACHE_DURATION) {
        console.log(`📈 Using cached ${type} analytics data`);
        return cached.data;
      }

      console.log(`🔄 Computing fresh ${type} analytics data`);

      // Get trades for analytics calculation
      const trades = await this.getAllTrades();

      // Placeholder for actual analytics calculations
      const analyticsData = {
        type,
        params,
        timestamp: now,
        tradesCount: trades.length,
        // Specific data based on type
        ...(type === 'performance' && {
          totalReturn: 0,
          sharpeRatio: 0,
          winRate: 0,
          profitFactor: 0
        }),
        ...(type === 'risk' && {
          maxDrawdown: 0,
          volatility: 0,
          var95: 0,
          beta: 0
        }),
        ...(type === 'metrics' && {
          avgHoldingPeriod: 0,
          avgGain: 0,
          avgLoss: 0,
          largestWin: 0,
          largestLoss: 0
        })
      };

      this.analyticsCache.set(cacheKey, {
        data: analyticsData,
        timestamp: now,
        userId
      });

      return analyticsData;
    } catch (error) {
      console.error('❌ Failed to get analytics data:', error);
      return null;
    }
  }

  // PERFORMANCE: Specialized cache invalidation methods
  static invalidateMonthlyPerformanceCache(userId?: string): void {
    if (userId) {
      // Clear specific user's monthly performance cache
      for (const [key] of this.monthlyPerformanceCache) {
        if (key.startsWith(`${userId}_monthly_`)) {
          this.monthlyPerformanceCache.delete(key);
        }
      }
    } else {
      this.monthlyPerformanceCache.clear();
    }
    console.log('🗑️ Monthly performance cache invalidated');
  }

  static invalidateChartViewerCache(userId?: string): void {
    if (userId) {
      // Clear specific user's chart viewer cache
      for (const [key] of this.chartViewerCache) {
        if (key.startsWith(`${userId}_charts_`)) {
          this.chartViewerCache.delete(key);
        }
      }
    } else {
      this.chartViewerCache.clear();
    }
    console.log('🗑️ Chart viewer cache invalidated');
  }

  static invalidateDrawdownCache(userId?: string): void {
    if (userId) {
      // Clear specific user's drawdown cache
      for (const [key] of this.drawdownCache) {
        if (key.startsWith(`${userId}_drawdown_`)) {
          this.drawdownCache.delete(key);
        }
      }
    } else {
      this.drawdownCache.clear();
    }
    console.log('🗑️ Drawdown cache invalidated');
  }

  static invalidateAnalyticsCache(userId?: string, type?: string): void {
    if (userId) {
      // Clear specific user's analytics cache
      for (const [key] of this.analyticsCache) {
        const shouldDelete = type
          ? key.startsWith(`${userId}_analytics_${type}_`)
          : key.startsWith(`${userId}_analytics_`);

        if (shouldDelete) {
          this.analyticsCache.delete(key);
        }
      }
    } else {
      this.analyticsCache.clear();
    }
    console.log(`🗑️ ${type || 'All'} analytics cache invalidated`);
  }

  // OPTIMIZED: Minimal cache invalidation for single trade saves
  static invalidateTradeRelatedCaches(userId?: string): void {
    this.invalidateMonthlyPerformanceCache(userId);
    this.invalidateDrawdownCache(userId);
    this.invalidateAnalyticsCache(userId);
    // Minimal logging for performance
  }

  // CRITICAL FIX: Selective cache invalidation that preserves monthly performance cache
  // Use this for operations that don't affect historical portfolio data
  static invalidateTradeRelatedCachesExceptMonthlyPerformance(userId?: string): void {
    this.invalidateDrawdownCache(userId);
    this.invalidateAnalyticsCache(userId);
    // Preserve monthly performance cache for bulk imports/operations
    console.log('🗑️ Trade-related caches invalidated (preserved monthly performance cache)');
  }

  // PERFORMANCE: Invalidate chart-related caches when charts change
  static invalidateChartRelatedCaches(userId?: string): void {
    this.invalidateChartViewerCache(userId);
    console.log('🗑️ Chart-related caches invalidated');
  }

  // PERFORMANCE: Complete cache invalidation for major data changes
  static invalidateAllUserCaches(userId?: string): void {
    this.clearMiscDataCache();
    this.invalidateTradeRelatedCaches(userId);
    this.invalidateChartRelatedCaches(userId);
    if (userId) {
      this.clearTradesCache(userId);
    } else {
      this.tradesCache.clear();
    }
    console.log('🗑️ All user caches completely invalidated');
  }

  // PERFORMANCE: Smart cache invalidation based on operation type
  static invalidateCachesByOperation(operation: 'trade_save' | 'trade_delete' | 'chart_upload' | 'chart_delete' | 'settings_change' | 'logout', userId?: string): void {
    switch (operation) {
      case 'trade_save':
      case 'trade_delete':
        this.clearTradesCache(userId);
        this.invalidateTradeRelatedCaches(userId);
        break;
      case 'chart_upload':
      case 'chart_delete':
        this.invalidateChartRelatedCaches(userId);
        break;
      case 'settings_change':
        this.clearMiscDataCache();
        break;
      case 'logout':
        this.invalidateAllUserCaches(userId);
        break;
    }
    console.log(`🗑️ Caches invalidated for operation: ${operation}`);
  }

  static async deleteMiscData(key: string): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) {
        // User not authenticated - return false silently for guest mode
        return false
      }

      const { error } = await supabase
        .from('misc_data')
        .delete()
        .eq('user_id', userId)
        .eq('key', key)

      if (error) throw error

      return true
    } catch (error) {
      console.error('❌ Failed to delete misc data from Supabase:', error)
      return false
    }
  }

  // ===== CHART IMAGE BLOBS =====

  static async saveChartImageBlob(imageBlob: any): Promise<boolean> {


    try {
      const userId = await AuthService.getUserId()
      if (!userId) {

        throw new Error('User not authenticated')
      }



      // Convert base64 to binary for bytea storage
      let binaryData: Uint8Array;
      try {
        binaryData = Uint8Array.from(atob(imageBlob.data), c => c.charCodeAt(0))
      } catch (conversionError) {
        throw new Error('Failed to convert base64 data')
      }

      const insertData = {
        id: imageBlob.id,
        user_id: userId,
        trade_id: imageBlob.trade_id,
        image_type: imageBlob.image_type,
        filename: imageBlob.filename,
        mime_type: imageBlob.mime_type,
        size_bytes: imageBlob.size_bytes,
        data: binaryData,
        uploaded_at: imageBlob.uploaded_at,
        compressed: imageBlob.compressed || false,
        original_size: imageBlob.original_size
      };



      const { data: insertResult, error } = await supabase
        .from('chart_image_blobs')
        .insert(insertData)
        .select()

      if (error) {

        throw error
      }

      // CRITICAL FIX: Invalidate chart-related caches after chart upload
      this.invalidateChartRelatedCaches(userId);
      console.log('🗑️ Chart-related caches invalidated after upload');

      return true
    } catch (error) {
      console.error('❌ Failed to save chart image blob to Supabase:', error)
      return false
    }
  }

  static async getChartImageBlob(blobId: string): Promise<any | null> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')



      // First, get metadata without the binary data to avoid 406 errors
      const { data: metadata, error: metadataError } = await supabase
        .from('chart_image_blobs')
        .select('id, user_id, trade_id, image_type, filename, mime_type, size_bytes, uploaded_at, compressed, original_size, created_at, updated_at')
        .eq('user_id', userId)
        .eq('id', blobId)
        .single()

      if (metadataError) {
        if (metadataError.code === 'PGRST116') {
          // No rows returned
          return null
        }
        throw metadataError
      }



      // Now get the binary data separately
      const { data: binaryData, error: binaryError } = await supabase
        .from('chart_image_blobs')
        .select('data')
        .eq('user_id', userId)
        .eq('id', blobId)
        .single()

      if (binaryError) {

        throw binaryError
      }



      // Combine metadata and binary data
      const result = {
        ...metadata,
        data: binaryData.data
      }


      return result
    } catch (error) {
      console.error('❌ Failed to get chart image blob:', error)
      return null
    }
  }

  static async getAllChartImageBlobs(): Promise<any[]> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')



      // Get metadata only (without binary data) for listing
      // Binary data will be fetched individually when needed
      const { data, error } = await supabase
        .from('chart_image_blobs')
        .select('id, user_id, trade_id, image_type, filename, mime_type, size_bytes, uploaded_at, compressed, original_size, created_at, updated_at')
        .eq('user_id', userId)
        .order('uploaded_at', { ascending: false })

      if (error) throw error


      return data || []
    } catch (error) {
      console.error('❌ Failed to get all chart image blobs:', error)
      return []
    }
  }

  static async deleteChartImageBlob(blobId: string): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')



      const { error } = await supabase
        .from('chart_image_blobs')
        .delete()
        .eq('user_id', userId)
        .eq('id', blobId)

      if (error) throw error

      // CRITICAL FIX: Invalidate chart-related caches after chart deletion
      this.invalidateChartRelatedCaches(userId);
      console.log('🗑️ Chart-related caches invalidated after deletion');

      return true
    } catch (error) {
      console.error('❌ Failed to delete chart image blob:', error)
      return false
    }
  }

  static async getTradeChartImageBlobs(tradeId: string): Promise<any[]> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      // NEW: Check if tradeId is a valid UUID format
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(tradeId);

      if (!isUUID) {
        console.log(`📦 [SUPABASE] Trade ID is not UUID format, skipping chart blob query: ${tradeId}`);
        return [];
      }

      const { data, error } = await supabase
        .from('chart_image_blobs')
        .select('*')
        .eq('trade_id', tradeId)
        .eq('user_id', userId)

      if (error) throw error

      return data || []
    } catch (error) {
      console.error('❌ Failed to get trade chart image blobs:', error)
      return []
    }
  }



  static async deleteTradeChartImageBlobs(tradeId: string): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      // NEW: Check if tradeId is a valid UUID format
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(tradeId);

      if (!isUUID) {
        console.log(`📦 [SUPABASE] Trade ID is not UUID format, skipping chart blob deletion: ${tradeId}`);
        return true; // Return true since there's nothing to delete for non-UUID trades
      }

      const { error } = await supabase
        .from('chart_image_blobs')
        .delete()
        .eq('trade_id', tradeId)
        .eq('user_id', userId)

      if (error) throw error

      return true
    } catch (error) {
      console.error('❌ Failed to delete trade chart image blobs:', error)
      return false
    }
  }

  static async updateChartImageBlobTradeId(blobId: string, newTradeId: string): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('chart_image_blobs')
        .update({ trade_id: newTradeId })
        .eq('id', blobId)
        .eq('user_id', userId)

      if (error) throw error


      return true
    } catch (error) {
      console.error('❌ Failed to update chart image blob trade ID:', error)
      return false
    }
  }

  // ===== DASHBOARD CONFIG =====

  static async getDashboardConfig(): Promise<any | null> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('dashboard_config')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') throw error

      return data || null
    } catch (error) {
      console.error('❌ Failed to get dashboard config from Supabase:', error)
      return null
    }
  }

  static async saveDashboardConfig(config: any): Promise<boolean> {
    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('dashboard_config')
        .upsert({
          user_id: userId,
          config
        }, {
          onConflict: 'user_id'
        })

      if (error) throw error

      console.log('✅ Saved dashboard config to Supabase')
      return true
    } catch (error) {
      console.error('❌ Failed to save dashboard config to Supabase:', error)
      return false
    }
  }

  // ===== COMMENTARY DATA REMOVED =====
  // Commentary system has been simplified to static display only

  // ===== UTILITIES =====

  static async clearAllData(confirmDestruction: boolean = false): Promise<boolean> {
    // CRITICAL SECURITY FIX: Add explicit confirmation requirement
    if (!confirmDestruction) {
      console.error('❌ SECURITY BLOCK: clearAllData requires explicit confirmation');
      console.error('❌ This operation DELETES ALL user data permanently');
      console.error('❌ Call with confirmDestruction=true only if absolutely certain');
      throw new Error('clearAllData requires explicit confirmation - this operation deletes all user data');
    }

    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      console.warn('⚠️ DESTRUCTIVE OPERATION: Clearing ALL user data');
      console.warn('⚠️ This will permanently delete all application data');

      // Delete all user data from all tables EXCEPT portfolio_data
      // CRITICAL FIX: Preserve portfolio_data (yearly capitals, monthly overrides, capital changes)
      const tables = [
        'trades',
        'chart_image_blobs',
        'user_preferences',
        // 'portfolio_data', // REMOVED: Preserve monthly performance data
        'tax_data',
        // 'milestones_data', // REMOVED: Milestone functionality removed
        'misc_data',
        'trade_settings',
        'dashboard_config',
        'commentary_data'
      ]

      for (const table of tables) {
        const { error } = await supabase
          .from(table)
          .delete()
          .eq('user_id', userId)

        if (error) throw error
      }

      console.log('✅ Cleared all user data from Supabase (preserved portfolio_data)')
      return true
    } catch (error) {
      console.error('❌ Failed to clear all data from Supabase:', error)
      return false
    }
  }

  // CRITICAL FIX: New function to clear only trades data (preserves portfolio data)
  static async clearTradesOnly(confirmDestruction: boolean = false): Promise<boolean> {
    // CRITICAL SECURITY FIX: Add explicit confirmation requirement
    if (!confirmDestruction) {
      console.error('❌ SECURITY BLOCK: clearTradesOnly requires explicit confirmation');
      console.error('❌ This operation DELETES ALL trade data permanently');
      console.error('❌ Call with confirmDestruction=true only if absolutely certain');
      throw new Error('clearTradesOnly requires explicit confirmation - this operation deletes all trade data');
    }

    try {
      const userId = await AuthService.getUserId()
      if (!userId) throw new Error('User not authenticated')

      console.warn('⚠️ DESTRUCTIVE OPERATION: Clearing ALL trade data');

      // Delete only trades and trade-related data, preserve portfolio_data
      const { error } = await supabase
        .from('trades')
        .delete()
        .eq('user_id', userId)

      if (error) throw error

      console.log('✅ Cleared only trades data from Supabase (preserved portfolio_data)')
      return true
    } catch (error) {
      console.error('❌ Failed to clear trades data from Supabase:', error)
      return false
    }
  }
}
