name: 'Nexus Journal Complete Database Backup'

on:
  # Daily backup at 2 AM UTC
  schedule:
    - cron: '0 2 * * *'

  # Backup on pull requests and pushes to main
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

  # Manual trigger with backup options
  workflow_dispatch:
    inputs:
      backup_type:
        description: 'Type of backup to perform'
        required: true
        default: 'schema-only'
        type: choice
        options:
        - complete
        - data-only
        - schema-only
        - auth-only
      include_binary_data:
        description: 'Include chart images and binary data'
        required: true
        default: 'true'
        type: choice
        options:
        - 'true'
        - 'false'

jobs:
  complete_database_backup:
    name: 'Complete Database Backup'
    runs-on: ubuntu-latest

    permissions:
      contents: write
      actions: read

    env:
      SUPABASE_DB_URL: ${{ secrets.SUPABASE_DB_URL }}
      BACKUP_ENCRYPTION_KEY: ${{ secrets.BACKUP_ENCRYPTION_KEY }}

    steps:
      # Checkout repository
      - name: 'Checkout Repository'
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref || github.ref }}
          fetch-depth: 0
          lfs: true

      # Setup Git LFS for large backup files
      - name: 'Setup Git LFS'
        run: |
          git lfs install
          echo "✅ Git LFS installed and configured"

      # Setup Supabase CLI
      - name: 'Setup Supabase CLI'
        uses: supabase/setup-cli@v1
        with:
          version: latest

      # Setup PostgreSQL client tools
      - name: 'Setup PostgreSQL Client'
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client
          echo "✅ PostgreSQL client installed"

      # Verify database connection
      - name: 'Verify Database Connection'
        run: |
          echo "🔍 Testing Supabase database connection..."
          if [ -z "$SUPABASE_DB_URL" ]; then
            echo "❌ SUPABASE_DB_URL secret is not set"
            exit 1
          fi

          # Test connection
          psql "$SUPABASE_DB_URL" -c "SELECT version();" > /dev/null 2>&1
          if [ $? -eq 0 ]; then
            echo "✅ Database connection successful"
          else
            echo "❌ Database connection failed"
            exit 1
          fi

      # Create timestamped backup directory
      - name: 'Create Backup Directory Structure'
        run: |
          TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
          BACKUP_DIR="backups/nexus-journal/${TIMESTAMP}"
          mkdir -p "$BACKUP_DIR"/{auth,public,metadata}
          echo "BACKUP_DIR=$BACKUP_DIR" >> $GITHUB_ENV
          echo "TIMESTAMP=$TIMESTAMP" >> $GITHUB_ENV
          echo "📁 Created backup directory: $BACKUP_DIR"

      # ===== AUTHENTICATION & OAUTH DATA BACKUP =====

      # Backup authentication schema and roles
      - name: 'Backup Authentication Schema & Roles'
        if: ${{ github.event.inputs.backup_type != 'data-only' }}
        continue-on-error: true
        run: |
          echo "🔐 Backing up authentication schema and roles..."

          # Backup database roles and permissions (Supabase compatible)
          echo "-- Database roles and permissions backup" > "$BACKUP_DIR/auth/roles.sql"
          if ! psql "$SUPABASE_DB_URL" -c "SELECT rolname, rolsuper, rolinherit, rolcreaterole, rolcreatedb, rolcanlogin FROM pg_roles;" >> "$BACKUP_DIR/auth/roles.sql" 2>/dev/null; then
            echo "-- Roles query skipped (insufficient permissions)" >> "$BACKUP_DIR/auth/roles.sql"
            echo "⚠️ Roles backup failed, continuing..."
          fi

          # Backup auth schema structure
          if ! psql "$SUPABASE_DB_URL" -c "\dt auth.*" > "$BACKUP_DIR/auth/auth_tables_list.txt" 2>/dev/null; then
            echo "Auth tables list failed" > "$BACKUP_DIR/auth/auth_tables_list.txt"
            echo "⚠️ Auth tables list failed, continuing..."
          fi

          # Backup auth schema structure
          if ! pg_dump "$SUPABASE_DB_URL" --schema-only --schema=auth --file="$BACKUP_DIR/auth/auth_schema.sql" 2>/dev/null; then
            echo "-- Auth schema backup failed" > "$BACKUP_DIR/auth/auth_schema.sql"
            echo "⚠️ Auth schema backup failed, continuing..."
          fi

          echo "✅ Authentication schema backup completed (with any failures noted)"

      # Backup OAuth and user authentication data
      - name: 'Backup OAuth & Authentication Data'
        if: ${{ github.event.inputs.backup_type != 'schema-only' }}
        continue-on-error: true
        run: |
          echo "👤 Backing up OAuth and authentication data..."

          # Backup auth.users table (user accounts)
          if ! pg_dump "$SUPABASE_DB_URL" --data-only --table=auth.users --file="$BACKUP_DIR/auth/users.sql" 2>/dev/null; then
            echo "-- Users table backup failed" > "$BACKUP_DIR/auth/users.sql"
            echo "⚠️ Users table backup failed, continuing..."
          fi

          # Backup auth.identities table (OAuth provider data)
          if ! pg_dump "$SUPABASE_DB_URL" --data-only --table=auth.identities --file="$BACKUP_DIR/auth/identities.sql" 2>/dev/null; then
            echo "-- Identities table backup failed" > "$BACKUP_DIR/auth/identities.sql"
            echo "⚠️ Identities table backup failed, continuing..."
          fi

          # Backup auth.sessions table (active sessions)
          if ! pg_dump "$SUPABASE_DB_URL" --data-only --table=auth.sessions --file="$BACKUP_DIR/auth/sessions.sql" 2>/dev/null; then
            echo "-- Sessions table backup failed" > "$BACKUP_DIR/auth/sessions.sql"
            echo "⚠️ Sessions table backup failed, continuing..."
          fi

          # Backup auth.refresh_tokens table (JWT tokens)
          if ! pg_dump "$SUPABASE_DB_URL" --data-only --table=auth.refresh_tokens --file="$BACKUP_DIR/auth/refresh_tokens.sql" 2>/dev/null; then
            echo "-- Refresh tokens table backup failed" > "$BACKUP_DIR/auth/refresh_tokens.sql"
            echo "⚠️ Refresh tokens table backup failed, continuing..."
          fi

          # Backup additional auth tables if they exist
          if psql "$SUPABASE_DB_URL" -c "SELECT tablename FROM pg_tables WHERE schemaname = 'auth';" -t 2>/dev/null | while read table; do
            if [ ! -z "$table" ] && [ "$table" != "users" ] && [ "$table" != "identities" ] && [ "$table" != "sessions" ] && [ "$table" != "refresh_tokens" ]; then
              echo "📋 Backing up additional auth table: $table"
              if ! pg_dump "$SUPABASE_DB_URL" --data-only --table="auth.$table" --file="$BACKUP_DIR/auth/${table}.sql" 2>/dev/null; then
                echo "-- Additional table $table backup failed" > "$BACKUP_DIR/auth/${table}.sql"
                echo "⚠️ Additional auth table $table backup failed, continuing..."
              fi
            fi
          done; then
            echo "📋 Additional auth tables processed"
          else
            echo "⚠️ Could not list additional auth tables, continuing..."
          fi

          echo "✅ OAuth and authentication data backup completed (with any failures noted)"

      # ===== PUBLIC SCHEMA BACKUP =====

      # Backup public schema structure
      - name: 'Backup Public Schema Structure'
        if: ${{ github.event.inputs.backup_type != 'data-only' }}
        continue-on-error: true
        run: |
          echo "🏗️ Backing up public schema structure..."

          # Backup complete public schema structure using pg_dump directly
          if ! pg_dump "$SUPABASE_DB_URL" --schema-only --schema=public --file="$BACKUP_DIR/public/schema.sql" 2>/dev/null; then
            echo "-- Public schema backup failed" > "$BACKUP_DIR/public/schema.sql"
            echo "⚠️ Public schema backup failed, continuing..."
          fi

          # List all public tables
          if ! psql "$SUPABASE_DB_URL" -c "\dt public.*" > "$BACKUP_DIR/public/tables_list.txt" 2>/dev/null; then
            echo "Public tables list failed" > "$BACKUP_DIR/public/tables_list.txt"
            echo "⚠️ Public tables list failed, continuing..."
          fi

          echo "✅ Public schema structure backup completed (with any failures noted)"

      # Backup core trading data
      - name: 'Backup Core Trading Data'
        if: ${{ github.event.inputs.backup_type != 'schema-only' }}
        run: |
          echo "📊 Backing up core trading data..."

          # Backup trades table (main trading data)
          pg_dump "$SUPABASE_DB_URL" \
            --data-only \
            --table=public.trades \
            --file="$BACKUP_DIR/public/trades.sql"

          # Backup user preferences
          pg_dump "$SUPABASE_DB_URL" \
            --data-only \
            --table=public.user_preferences \
            --file="$BACKUP_DIR/public/user_preferences.sql"

          # Backup portfolio data (capitals, changes, overrides)
          pg_dump "$SUPABASE_DB_URL" \
            --data-only \
            --table=public.portfolio_data \
            --file="$BACKUP_DIR/public/portfolio_data.sql"

          # Backup trade settings
          pg_dump "$SUPABASE_DB_URL" \
            --data-only \
            --table=public.trade_settings \
            --file="$BACKUP_DIR/public/trade_settings.sql"

          # Backup miscellaneous data
          pg_dump "$SUPABASE_DB_URL" \
            --data-only \
            --table=public.misc_data \
            --file="$BACKUP_DIR/public/misc_data.sql"

          # Backup tax data
          pg_dump "$SUPABASE_DB_URL" \
            --data-only \
            --table=public.tax_data \
            --file="$BACKUP_DIR/public/tax_data.sql"

          # Backup dashboard configuration
          pg_dump "$SUPABASE_DB_URL" \
            --data-only \
            --table=public.dashboard_config \
            --file="$BACKUP_DIR/public/dashboard_config.sql"

          # Backup commentary data
          pg_dump "$SUPABASE_DB_URL" \
            --data-only \
            --table=public.commentary_data \
            --file="$BACKUP_DIR/public/commentary_data.sql"

          echo "✅ Core trading data backup completed"

      # Backup chart images and binary data
      - name: 'Backup Chart Images & Binary Data'
        if: ${{ github.event.inputs.backup_type != 'schema-only' && github.event.inputs.include_binary_data != 'false' }}
        run: |
          echo "🖼️ Backing up chart images and binary data..."

          # Backup chart image blobs (this can be large)
          pg_dump "$SUPABASE_DB_URL" \
            --data-only \
            --table=public.chart_image_blobs \
            --file="$BACKUP_DIR/public/chart_image_blobs.sql"

          # Create a summary of chart images
          psql "$SUPABASE_DB_URL" -c "
            SELECT
              COUNT(*) as total_images,
              SUM(size_bytes) as total_size_bytes,
              AVG(size_bytes) as avg_size_bytes,
              COUNT(DISTINCT user_id) as unique_users,
              COUNT(DISTINCT trade_id) as unique_trades
            FROM public.chart_image_blobs;
          " > "$BACKUP_DIR/public/chart_images_summary.txt"

          echo "✅ Chart images and binary data backup completed"

      # Backup any additional public tables
      - name: 'Backup Additional Public Tables'
        if: ${{ github.event.inputs.backup_type != 'schema-only' }}
        run: |
          echo "📋 Backing up any additional public tables..."

          # Get list of all public tables
          TABLES=$(psql "$SUPABASE_DB_URL" -t -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public';")

          # Known tables we've already backed up
          KNOWN_TABLES="trades user_preferences portfolio_data trade_settings misc_data tax_data dashboard_config commentary_data chart_image_blobs"

          for table in $TABLES; do
            table=$(echo $table | xargs) # trim whitespace
            if [ ! -z "$table" ] && ! echo "$KNOWN_TABLES" | grep -q "$table"; then
              echo "📋 Backing up additional table: $table"
              pg_dump "$SUPABASE_DB_URL" \
                --data-only \
                --table="public.$table" \
                --file="$BACKUP_DIR/public/${table}.sql" || true
            fi
          done

          echo "✅ Additional public tables backup completed"

      # ===== METADATA & VERIFICATION =====

      # Generate comprehensive backup metadata
      - name: 'Generate Backup Metadata'
        continue-on-error: true
        run: |
          echo "📋 Generating comprehensive backup metadata..."

          # Database information
          if ! psql "$SUPABASE_DB_URL" -c "SELECT version();" > "$BACKUP_DIR/metadata/database_version.txt" 2>/dev/null; then
            echo "Database version query failed" > "$BACKUP_DIR/metadata/database_version.txt"
            echo "⚠️ Database version query failed, continuing..."
          fi

          if ! psql "$SUPABASE_DB_URL" -c "SELECT current_database(), current_user, current_timestamp;" > "$BACKUP_DIR/metadata/database_info.txt" 2>/dev/null; then
            echo "Database info query failed" > "$BACKUP_DIR/metadata/database_info.txt"
            echo "⚠️ Database info query failed, continuing..."
          fi

          # Schema information
          if ! psql "$SUPABASE_DB_URL" -c "SELECT schemaname, COUNT(*) as table_count FROM pg_tables GROUP BY schemaname;" > "$BACKUP_DIR/metadata/schema_summary.txt" 2>/dev/null; then
            echo "Schema summary query failed" > "$BACKUP_DIR/metadata/schema_summary.txt"
            echo "⚠️ Schema summary query failed, continuing..."
          fi

          # User statistics (with error handling for schema differences)
          psql "$SUPABASE_DB_URL" -c "
            SELECT
              'Total Users' as metric,
              COUNT(*) as value
            FROM auth.users
            UNION ALL
            SELECT
              'Total Sessions' as metric,
              COUNT(*) as value
            FROM auth.sessions
            UNION ALL
            SELECT
              'OAuth Identities' as metric,
              COUNT(*) as value
            FROM auth.identities;
          " > "$BACKUP_DIR/metadata/user_statistics.txt" 2>/dev/null || echo "User statistics query failed - schema may have changed" > "$BACKUP_DIR/metadata/user_statistics.txt"

          # Trading data statistics
          psql "$SUPABASE_DB_URL" -c "
            SELECT
              'Total Trades' as metric,
              COUNT(*) as value
            FROM public.trades
            UNION ALL
            SELECT
              'Unique Users with Trades' as metric,
              COUNT(DISTINCT user_id) as value
            FROM public.trades
            UNION ALL
            SELECT
              'Chart Images' as metric,
              COUNT(*) as value
            FROM public.chart_image_blobs;
          " > "$BACKUP_DIR/metadata/trading_statistics.txt"

          # Create comprehensive backup manifest
          cat > "$BACKUP_DIR/metadata/backup_manifest.json" << EOF
          {
            "backup_info": {
              "timestamp": "$TIMESTAMP",
              "date": "$(date -u +"%Y-%m-%d %H:%M:%S UTC")",
              "trigger": "${{ github.event_name }}",
              "backup_type": "${{ github.event.inputs.backup_type || 'schema-only' }}",
              "include_binary_data": "${{ github.event.inputs.include_binary_data || 'true' }}",
              "commit_sha": "${{ github.sha }}",
              "branch": "${{ github.ref_name }}",
              "workflow_run_id": "${{ github.run_id }}",
              "repository": "${{ github.repository }}"
            },
            "database_info": {
              "connection_tested": true,
              "schemas_backed_up": ["auth", "public"],
              "backup_method": "pg_dump + supabase CLI"
            },
            "backup_contents": {
              "auth_schema": "auth_schema.sql",
              "auth_data": ["users.sql", "identities.sql", "sessions.sql", "refresh_tokens.sql"],
              "public_schema": "schema.sql",
              "trading_data": ["trades.sql", "portfolio_data.sql", "user_preferences.sql"],
              "binary_data": "chart_image_blobs.sql",
              "metadata": ["database_version.txt", "user_statistics.txt", "trading_statistics.txt"]
            }
          }
          EOF

          echo "✅ Backup metadata generated"

      # Verify backup integrity
      - name: 'Verify Backup Integrity'
        continue-on-error: true
        run: |
          echo "🔍 Verifying backup integrity..."

          # Check if critical files exist and have content (flexible for schema-only mode)
          CRITICAL_FILES=(
            "$BACKUP_DIR/public/schema.sql"
            "$BACKUP_DIR/metadata/backup_manifest.json"
          )

          # Additional files to check if they should exist
          OPTIONAL_FILES=(
            "$BACKUP_DIR/auth/auth_schema.sql"
            "$BACKUP_DIR/auth/roles.sql"
          )

          FAILED_COUNT=0

          for file in "${CRITICAL_FILES[@]}"; do
            if [ -f "$file" ] && [ -s "$file" ]; then
              echo "✅ $file exists and has content"
            else
              echo "⚠️ $file is missing or empty"
              FAILED_COUNT=$((FAILED_COUNT + 1))
            fi
          done

          for file in "${OPTIONAL_FILES[@]}"; do
            if [ -f "$file" ] && [ -s "$file" ]; then
              echo "✅ $file exists and has content"
            elif [ -f "$file" ]; then
              echo "⚠️ $file exists but is empty"
            else
              echo "ℹ️ $file not found (may be expected for this backup type)"
            fi
          done

          if [ $FAILED_COUNT -gt 0 ]; then
            echo "⚠️ $FAILED_COUNT critical files failed, but continuing..."
          fi

          # Calculate backup sizes
          AUTH_SIZE=$(du -sh "$BACKUP_DIR/auth" | cut -f1)
          PUBLIC_SIZE=$(du -sh "$BACKUP_DIR/public" | cut -f1)
          TOTAL_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)

          echo "📊 Backup sizes:"
          echo "  Auth data: $AUTH_SIZE"
          echo "  Public data: $PUBLIC_SIZE"
          echo "  Total: $TOTAL_SIZE"

          # Store sizes in environment for later use
          echo "AUTH_SIZE=$AUTH_SIZE" >> $GITHUB_ENV
          echo "PUBLIC_SIZE=$PUBLIC_SIZE" >> $GITHUB_ENV
          echo "TOTAL_SIZE=$TOTAL_SIZE" >> $GITHUB_ENV

          echo "✅ Backup integrity verification completed"

      # Generate comprehensive backup documentation
      - name: 'Generate Backup Documentation'
        run: |
          echo "📚 Generating backup documentation..."

          cat > "$BACKUP_DIR/README.md" << EOF
          # Nexus Journal Complete Database Backup

          **Backup Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          **Backup Type:** ${{ github.event.inputs.backup_type || 'schema-only' }}
          **Binary Data Included:** ${{ github.event.inputs.include_binary_data || 'true' }}
          **Triggered By:** ${{ github.event_name }}
          **Commit SHA:** ${{ github.sha }}
          **Workflow Run:** [${{ github.run_id }}](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

          ## 📊 Backup Contents

          ### Authentication Data (auth/)
          | File | Description | Size |
          |------|-------------|------|
          | \`roles.sql\` | Database roles and permissions | $([ -f "$BACKUP_DIR/auth/roles.sql" ] && du -h "$BACKUP_DIR/auth/roles.sql" | cut -f1 || echo "N/A") |
          | \`auth_schema.sql\` | Authentication schema structure | $([ -f "$BACKUP_DIR/auth/auth_schema.sql" ] && du -h "$BACKUP_DIR/auth/auth_schema.sql" | cut -f1 || echo "N/A") |
          | \`users.sql\` | User accounts and profiles | $([ -f "$BACKUP_DIR/auth/users.sql" ] && du -h "$BACKUP_DIR/auth/users.sql" | cut -f1 || echo "N/A") |
          | \`identities.sql\` | OAuth provider identities | $([ -f "$BACKUP_DIR/auth/identities.sql" ] && du -h "$BACKUP_DIR/auth/identities.sql" | cut -f1 || echo "N/A") |
          | \`sessions.sql\` | Active user sessions | $([ -f "$BACKUP_DIR/auth/sessions.sql" ] && du -h "$BACKUP_DIR/auth/sessions.sql" | cut -f1 || echo "N/A") |
          | \`refresh_tokens.sql\` | JWT refresh tokens | $([ -f "$BACKUP_DIR/auth/refresh_tokens.sql" ] && du -h "$BACKUP_DIR/auth/refresh_tokens.sql" | cut -f1 || echo "N/A") |

          ### Application Data (public/)
          | File | Description | Size |
          |------|-------------|------|
          | \`schema.sql\` | Database structure and tables | $([ -f "$BACKUP_DIR/public/schema.sql" ] && du -h "$BACKUP_DIR/public/schema.sql" | cut -f1 || echo "N/A") |
          | \`trades.sql\` | Core trading data | $([ -f "$BACKUP_DIR/public/trades.sql" ] && du -h "$BACKUP_DIR/public/trades.sql" | cut -f1 || echo "N/A") |
          | \`portfolio_data.sql\` | Portfolio capitals and changes | $([ -f "$BACKUP_DIR/public/portfolio_data.sql" ] && du -h "$BACKUP_DIR/public/portfolio_data.sql" | cut -f1 || echo "N/A") |
          | \`user_preferences.sql\` | User settings and preferences | $([ -f "$BACKUP_DIR/public/user_preferences.sql" ] && du -h "$BACKUP_DIR/public/user_preferences.sql" | cut -f1 || echo "N/A") |
          | \`chart_image_blobs.sql\` | Chart images and binary data | $([ -f "$BACKUP_DIR/public/chart_image_blobs.sql" ] && du -h "$BACKUP_DIR/public/chart_image_blobs.sql" | cut -f1 || echo "N/A") |
          | \`misc_data.sql\` | Miscellaneous user data | $([ -f "$BACKUP_DIR/public/misc_data.sql" ] && du -h "$BACKUP_DIR/public/misc_data.sql" | cut -f1 || echo "N/A") |

          **Total Backup Size:** $TOTAL_SIZE

          ## 🔄 Complete Restoration Instructions

          ### Prerequisites
          \`\`\`bash
          # Install PostgreSQL client
          sudo apt-get install postgresql-client

          # Install Supabase CLI
          npm install -g supabase
          \`\`\`

          ### Step 1: Restore Authentication Schema & Roles
          \`\`\`bash
          # Restore database roles first
          psql -d "your-database-url" -f auth/roles.sql

          # Restore auth schema structure
          psql -d "your-database-url" -f auth/auth_schema.sql
          \`\`\`

          ### Step 2: Restore Public Schema
          \`\`\`bash
          # Restore public schema structure
          psql -d "your-database-url" -f public/schema.sql
          \`\`\`

          ### Step 3: Restore Authentication Data
          \`\`\`bash
          # Restore user accounts
          psql -d "your-database-url" -f auth/users.sql

          # Restore OAuth identities
          psql -d "your-database-url" -f auth/identities.sql

          # Restore sessions (optional - these expire)
          psql -d "your-database-url" -f auth/sessions.sql

          # Restore refresh tokens (optional - these expire)
          psql -d "your-database-url" -f auth/refresh_tokens.sql
          \`\`\`

          ### Step 4: Restore Application Data
          \`\`\`bash
          # Restore core trading data
          psql -d "your-database-url" -f public/trades.sql

          # Restore portfolio data
          psql -d "your-database-url" -f public/portfolio_data.sql

          # Restore user preferences
          psql -d "your-database-url" -f public/user_preferences.sql

          # Restore other application data
          psql -d "your-database-url" -f public/misc_data.sql
          psql -d "your-database-url" -f public/trade_settings.sql
          psql -d "your-database-url" -f public/tax_data.sql
          psql -d "your-database-url" -f public/dashboard_config.sql

          # Restore chart images (large file - optional)
          psql -d "your-database-url" -f public/chart_image_blobs.sql
          \`\`\`

          ### Step 5: Verify Restoration
          \`\`\`bash
          # Check user count
          psql -d "your-database-url" -c "SELECT COUNT(*) FROM auth.users;"

          # Check trades count
          psql -d "your-database-url" -c "SELECT COUNT(*) FROM public.trades;"

          # Check OAuth identities
          psql -d "your-database-url" -c "SELECT provider, COUNT(*) FROM auth.identities GROUP BY provider;"
          \`\`\`

          ## 🔐 Security Notice

          ⚠️ **CRITICAL SECURITY WARNING:**

          This backup contains **HIGHLY SENSITIVE DATA** including:
          - User authentication credentials and OAuth tokens
          - Personal trading data and financial information
          - Session tokens and refresh tokens
          - User personal information and preferences

          **Security Requirements:**
          1. ✅ Repository MUST remain **PRIVATE**
          2. ✅ Access restricted to authorized personnel only
          3. ✅ Regular access audits required
          4. ✅ Consider additional encryption for sensitive backups
          5. ✅ Monitor for unauthorized access attempts
          6. ✅ Follow data protection regulations (GDPR, etc.)

          ## 📞 Support

          For restoration assistance or issues:
          1. Check the workflow logs: [Run ${{ github.run_id }}](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
          2. Verify database connection strings
          3. Ensure proper permissions for restoration
          4. Contact system administrator if needed

          ---
          *Backup generated by Nexus Journal Automated Backup System*
          EOF

          echo "✅ Backup documentation generated"

      # Commit and push all backup files
      - name: 'Commit Complete Backup'
        continue-on-error: true
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: |
            🗄️ Nexus Journal Database Backup - ${{ env.TIMESTAMP }}

            📊 Backup Details:
            - Type: ${{ github.event.inputs.backup_type || 'schema-only' }}
            - Binary Data: ${{ github.event.inputs.include_binary_data || 'true' }}
            - Triggered by: ${{ github.event_name }}
            - Auth data size: ${{ env.AUTH_SIZE || 'N/A' }}
            - Public data size: ${{ env.PUBLIC_SIZE || 'N/A' }}
            - Total size: ${{ env.TOTAL_SIZE || 'N/A' }}

            🔐 Backup Contents (may include partial data if some steps failed):
            ✅ Database schema and structure
            ✅ Authentication schema (if applicable)
            ✅ Backup metadata and documentation
            ⚠️ Some components may have failed - check workflow logs

            📅 Date: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
            🔗 Workflow: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
          file_pattern: 'backups/**'
          commit_user_name: 'nexus-backup-bot'
          commit_user_email: '<EMAIL>'
          commit_author: 'Nexus Backup Bot <<EMAIL>>'
          push_options: '--force-with-lease'

      # Retry push if initial commit failed (for large backups)
      - name: 'Retry Push for Large Backup'
        if: failure()
        continue-on-error: true
        run: |
          echo "🔄 Retrying git push for large backup..."
          git config --global http.postBuffer 524288000  # 500MB buffer
          git config --global http.lowSpeedLimit 0
          git config --global http.lowSpeedTime 999999

          # Try pushing with increased timeout
          timeout 1800 git push origin HEAD || echo "⚠️ Push retry failed, backup files are committed locally"

          echo "📁 Backup files are safely committed to local repository"
          echo "🔄 Next workflow run will attempt to push again"

      # Clean up old backups (keep last 30 days for daily backups)
      - name: 'Cleanup Old Backups'
        if: github.event_name == 'schedule'
        run: |
          echo "🧹 Cleaning up old backups..."

          # Find and remove backup directories older than 30 days
          find backups -type d -name "20*" -mtime +30 -exec rm -rf {} + 2>/dev/null || true

          # Count remaining backups
          BACKUP_COUNT=$(find backups -type d -name "20*" | wc -l)
          echo "📊 Remaining backups after cleanup: $BACKUP_COUNT"

          # Commit cleanup if any files were removed
          if [ -n "$(git status --porcelain)" ]; then
            git config user.name "nexus-backup-bot"
            git config user.email "<EMAIL>"
            git add -A
            git commit -m "🧹 Automated cleanup: Removed backups older than 30 days (keeping $BACKUP_COUNT backups)"
            git push
          fi

      # Final backup completion summary
      - name: 'Backup Completion Summary'
        run: |
          echo "🎉 Complete database backup finished successfully!"
          echo ""
          echo "📁 Backup Details:"
          echo "  📍 Location: $BACKUP_DIR"
          echo "  ⏰ Timestamp: $TIMESTAMP"
          echo "  📊 Type: ${{ github.event.inputs.backup_type || 'schema-only' }}"
          echo "  🖼️ Binary data: ${{ github.event.inputs.include_binary_data || 'true' }}"
          echo "  📏 Auth data: $AUTH_SIZE"
          echo "  📏 Public data: $PUBLIC_SIZE"
          echo "  📏 Total size: $TOTAL_SIZE"
          echo ""
          echo "🔐 Security Reminder:"
          echo "  ⚠️  This backup contains sensitive authentication and trading data"
          echo "  ✅ Ensure repository remains PRIVATE"
          echo "  ✅ Restrict access to authorized personnel only"
          echo "  ✅ Monitor for unauthorized access"
          echo ""
          echo "🔗 Links:"
          echo "  📊 Workflow run: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          echo "  📁 Backup location: backups/nexus-journal/$TIMESTAMP/"
          echo ""
          echo "✅ Backup automation completed successfully!"