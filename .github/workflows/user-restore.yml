name: 'User-Specific Data Restore'

on:
  workflow_dispatch:
    inputs:
      restore_type:
        description: 'Type of user restore to perform'
        required: true
        default: 'single-user'
        type: choice
        options:
          - single-user
          - bulk-users
          - user-data-only
          - user-auth-only
      user_ids:
        description: 'User ID(s) to restore (comma-separated for bulk: user1,user2,user3)'
        required: true
        type: string
      backup_date:
        description: 'Backup date to restore from (YYYYMMDD_HHMMSS format, or "latest")'
        required: true
        default: 'latest'
        type: string
      data_types:
        description: 'Data types to restore'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - trading-only
          - auth-only
          - portfolio-only
          - preferences-only
      target_database:
        description: 'Target database (use "production" with extreme caution!)'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production

jobs:
  user_restore:
    name: 'User-Specific Restore'
    runs-on: ubuntu-latest
    
    env:
      SUPABASE_DB_URL: ${{ secrets.SUPABASE_DB_URL }}
      BACKUP_ENCRYPTION_KEY: ${{ secrets.BACKUP_ENCRYPTION_KEY }}
      TARGET_DB_URL: ${{ secrets.SUPABASE_DB_URL }}  # Can be different for staging/dev
    
    steps:
      # Checkout repository to access backup files
      - name: 'Checkout Repository'
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Get all history to access backup files
      
      # Setup PostgreSQL client tools
      - name: 'Setup PostgreSQL Client'
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client
          echo "✅ PostgreSQL client installed"
      
      # Setup Supabase CLI
      - name: 'Setup Supabase CLI'
        uses: supabase/setup-cli@v1
        with:
          version: latest
      
      # Verify target database connection
      - name: 'Verify Target Database Connection'
        run: |
          echo "🔍 Testing target database connection..."
          if [ -z "$TARGET_DB_URL" ]; then
            echo "❌ TARGET_DB_URL secret is not set"
            exit 1
          fi
          
          echo "🔗 Testing connection to target database..."
          if psql "$TARGET_DB_URL" -c "SELECT version();" 2>&1; then
            echo "✅ Target database connection successful"
          else
            echo "❌ Target database connection failed"
            exit 1
          fi
      
      # Find and validate backup
      - name: 'Find and Validate Backup'
        run: |
          echo "🔍 Finding backup for restore..."
          
          BACKUP_DATE="${{ github.event.inputs.backup_date }}"
          
          if [ "$BACKUP_DATE" = "latest" ]; then
            # Find the most recent backup
            BACKUP_DIR=$(find backups/nexus-journal -type d -name "20*" | sort -r | head -1)
            if [ -z "$BACKUP_DIR" ]; then
              echo "❌ No backups found"
              exit 1
            fi
            echo "📁 Using latest backup: $BACKUP_DIR"
          else
            # Use specific backup date
            BACKUP_DIR="backups/nexus-journal/$BACKUP_DATE"
            if [ ! -d "$BACKUP_DIR" ]; then
              echo "❌ Backup not found: $BACKUP_DIR"
              echo "Available backups:"
              find backups/nexus-journal -type d -name "20*" | sort -r | head -10
              exit 1
            fi
            echo "📁 Using specified backup: $BACKUP_DIR"
          fi
          
          # Store backup directory for later steps
          echo "BACKUP_DIR=$BACKUP_DIR" >> $GITHUB_ENV
          echo "✅ Backup validated: $BACKUP_DIR"
      
      # Parse and validate user IDs
      - name: 'Parse and Validate User IDs'
        run: |
          echo "👤 Parsing user IDs..."
          
          USER_IDS="${{ github.event.inputs.user_ids }}"
          RESTORE_TYPE="${{ github.event.inputs.restore_type }}"
          
          # Convert comma-separated list to array
          IFS=',' read -ra USER_ARRAY <<< "$USER_IDS"
          
          echo "📋 User IDs to restore:"
          for user_id in "${USER_ARRAY[@]}"; do
            # Trim whitespace
            user_id=$(echo "$user_id" | xargs)
            echo "  - $user_id"
          done
          
          # Validate user count based on restore type
          USER_COUNT=${#USER_ARRAY[@]}
          
          if [ "$RESTORE_TYPE" = "single-user" ] && [ $USER_COUNT -ne 1 ]; then
            echo "❌ Single user restore requires exactly 1 user ID, got $USER_COUNT"
            exit 1
          fi
          
          if [ "$RESTORE_TYPE" = "bulk-users" ] && [ $USER_COUNT -lt 2 ]; then
            echo "❌ Bulk user restore requires at least 2 user IDs, got $USER_COUNT"
            exit 1
          fi
          
          # Store for later steps
          echo "USER_IDS=$USER_IDS" >> $GITHUB_ENV
          echo "USER_COUNT=$USER_COUNT" >> $GITHUB_ENV
          echo "✅ User IDs validated: $USER_COUNT users"
      
      # Create restore workspace
      - name: 'Create Restore Workspace'
        run: |
          echo "📁 Creating restore workspace..."
          
          TIMESTAMP=$(date -u +"%Y%m%d_%H%M%S")
          RESTORE_DIR="restore/user-restore-$TIMESTAMP"
          
          mkdir -p "$RESTORE_DIR/sql"
          mkdir -p "$RESTORE_DIR/logs"
          mkdir -p "$RESTORE_DIR/verification"
          
          echo "RESTORE_DIR=$RESTORE_DIR" >> $GITHUB_ENV
          echo "TIMESTAMP=$TIMESTAMP" >> $GITHUB_ENV
          echo "✅ Restore workspace created: $RESTORE_DIR"
      
      # Generate user-specific restore SQL
      - name: 'Generate User-Specific Restore SQL'
        continue-on-error: true
        run: |
          echo "🔧 Generating user-specific restore SQL..."
          
          USER_IDS="${{ env.USER_IDS }}"
          DATA_TYPES="${{ github.event.inputs.data_types }}"
          RESTORE_TYPE="${{ github.event.inputs.restore_type }}"
          
          # Convert comma-separated list to SQL IN clause
          IFS=',' read -ra USER_ARRAY <<< "$USER_IDS"
          SQL_USER_LIST=""
          for user_id in "${USER_ARRAY[@]}"; do
            user_id=$(echo "$user_id" | xargs)
            if [ -z "$SQL_USER_LIST" ]; then
              SQL_USER_LIST="'$user_id'"
            else
              SQL_USER_LIST="$SQL_USER_LIST,'$user_id'"
            fi
          done
          
          echo "📝 Generating restore SQL for users: $SQL_USER_LIST"
          
          # Create restore SQL file
          cat > "$RESTORE_DIR/sql/user_restore.sql" << EOF
          -- User-Specific Data Restore
          -- Generated: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          -- Restore Type: $RESTORE_TYPE
          -- Data Types: $DATA_TYPES
          -- User IDs: $USER_IDS
          -- Target Database: ${{ github.event.inputs.target_database }}
          
          BEGIN;
          
          -- Create temporary table for user IDs
          CREATE TEMP TABLE restore_users (user_id UUID);
          INSERT INTO restore_users (user_id) VALUES ($SQL_USER_LIST);
          
          EOF
          
          # Add data type specific restore commands
          if [ "$DATA_TYPES" = "all" ] || [ "$DATA_TYPES" = "auth-only" ]; then
            cat >> "$RESTORE_DIR/sql/user_restore.sql" << EOF
          -- Restore authentication data
          DELETE FROM auth.users WHERE id IN (SELECT user_id FROM restore_users);
          DELETE FROM auth.identities WHERE user_id IN (SELECT user_id FROM restore_users);
          DELETE FROM auth.sessions WHERE user_id IN (SELECT user_id FROM restore_users);
          
          EOF
          fi
          
          if [ "$DATA_TYPES" = "all" ] || [ "$DATA_TYPES" = "trading-only" ]; then
            cat >> "$RESTORE_DIR/sql/user_restore.sql" << EOF
          -- Restore trading data
          DELETE FROM public.trades WHERE user_id IN (SELECT user_id FROM restore_users);
          DELETE FROM public.chart_image_blobs WHERE user_id IN (SELECT user_id FROM restore_users);
          
          EOF
          fi
          
          if [ "$DATA_TYPES" = "all" ] || [ "$DATA_TYPES" = "portfolio-only" ]; then
            cat >> "$RESTORE_DIR/sql/user_restore.sql" << EOF
          -- Restore portfolio data
          DELETE FROM public.portfolio_data WHERE user_id IN (SELECT user_id FROM restore_users);
          
          EOF
          fi
          
          if [ "$DATA_TYPES" = "all" ] || [ "$DATA_TYPES" = "preferences-only" ]; then
            cat >> "$RESTORE_DIR/sql/user_restore.sql" << EOF
          -- Restore user preferences
          DELETE FROM public.user_preferences WHERE user_id IN (SELECT user_id FROM restore_users);
          DELETE FROM public.trade_settings WHERE user_id IN (SELECT user_id FROM restore_users);
          DELETE FROM public.misc_data WHERE user_id IN (SELECT user_id FROM restore_users);
          
          EOF
          fi
          
          cat >> "$RESTORE_DIR/sql/user_restore.sql" << EOF
          
          COMMIT;
          
          -- Verification queries
          SELECT 'Users restored:' as info, COUNT(*) as count FROM auth.users WHERE id IN (SELECT user_id FROM restore_users);
          SELECT 'Trades restored:' as info, COUNT(*) as count FROM public.trades WHERE user_id IN (SELECT user_id FROM restore_users);
          SELECT 'Portfolio entries restored:' as info, COUNT(*) as count FROM public.portfolio_data WHERE user_id IN (SELECT user_id FROM restore_users);
          
          EOF
          
          echo "✅ User-specific restore SQL generated"
          echo "📄 SQL file: $RESTORE_DIR/sql/user_restore.sql"

      # Execute the restore
      - name: 'Execute User-Specific Restore'
        continue-on-error: true
        run: |
          echo "🔄 Executing user-specific restore..."

          TARGET_DB="${{ github.event.inputs.target_database }}"
          USER_COUNT="${{ env.USER_COUNT }}"
          DATA_TYPES="${{ github.event.inputs.data_types }}"

          echo "📋 Restore Summary:"
          echo "  Target Database: $TARGET_DB"
          echo "  Users to Restore: $USER_COUNT"
          echo "  Data Types: $DATA_TYPES"
          echo "  Backup Source: $BACKUP_DIR"

          # Safety check for production
          if [ "$TARGET_DB" = "production" ]; then
            echo "🚨 PRODUCTION RESTORE WARNING 🚨"
            echo "This will OVERWRITE data for $USER_COUNT users!"
            echo "Proceeding in 5 seconds..."
            sleep 5
          fi

          # Execute restore from backup files
          echo "📦 Restoring data from backup files..."

          if [ "$DATA_TYPES" = "all" ] || [ "$DATA_TYPES" = "auth-only" ]; then
            if [ -f "$BACKUP_DIR/auth/users.sql" ]; then
              echo "👤 Restoring authentication data..."
              psql "$TARGET_DB_URL" -f "$BACKUP_DIR/auth/users.sql" > "$RESTORE_DIR/logs/auth_restore.log" 2>&1 || echo "⚠️ Auth restore failed"
            fi
          fi

          if [ "$DATA_TYPES" = "all" ] || [ "$DATA_TYPES" = "trading-only" ]; then
            if [ -f "$BACKUP_DIR/public/trades.sql" ]; then
              echo "📊 Restoring trading data..."
              psql "$TARGET_DB_URL" -f "$BACKUP_DIR/public/trades.sql" > "$RESTORE_DIR/logs/trades_restore.log" 2>&1 || echo "⚠️ Trading data restore failed"
            fi
          fi

          if [ "$DATA_TYPES" = "all" ] || [ "$DATA_TYPES" = "portfolio-only" ]; then
            if [ -f "$BACKUP_DIR/public/portfolio_data.sql" ]; then
              echo "💰 Restoring portfolio data..."
              psql "$TARGET_DB_URL" -f "$BACKUP_DIR/public/portfolio_data.sql" > "$RESTORE_DIR/logs/portfolio_restore.log" 2>&1 || echo "⚠️ Portfolio restore failed"
            fi
          fi

          if [ "$DATA_TYPES" = "all" ] || [ "$DATA_TYPES" = "preferences-only" ]; then
            if [ -f "$BACKUP_DIR/public/user_preferences.sql" ]; then
              echo "⚙️ Restoring user preferences..."
              psql "$TARGET_DB_URL" -f "$BACKUP_DIR/public/user_preferences.sql" > "$RESTORE_DIR/logs/preferences_restore.log" 2>&1 || echo "⚠️ Preferences restore failed"
            fi
          fi

          echo "✅ User-specific restore completed"

      # Verify restore results
      - name: 'Verify Restore Results'
        continue-on-error: true
        run: |
          echo "🔍 Verifying restore results..."

          USER_IDS="${{ env.USER_IDS }}"
          DATA_TYPES="${{ github.event.inputs.data_types }}"

          # Convert to SQL format
          IFS=',' read -ra USER_ARRAY <<< "$USER_IDS"
          SQL_USER_LIST=""
          for user_id in "${USER_ARRAY[@]}"; do
            user_id=$(echo "$user_id" | xargs)
            if [ -z "$SQL_USER_LIST" ]; then
              SQL_USER_LIST="'$user_id'"
            else
              SQL_USER_LIST="$SQL_USER_LIST,'$user_id'"
            fi
          done

          echo "📊 Verification Results:" > "$RESTORE_DIR/verification/restore_verification.txt"
          echo "Restore Date: $(date -u +"%Y-%m-%d %H:%M:%S UTC")" >> "$RESTORE_DIR/verification/restore_verification.txt"
          echo "User IDs: $USER_IDS" >> "$RESTORE_DIR/verification/restore_verification.txt"
          echo "Data Types: $DATA_TYPES" >> "$RESTORE_DIR/verification/restore_verification.txt"
          echo "" >> "$RESTORE_DIR/verification/restore_verification.txt"

          # Verify user accounts
          if [ "$DATA_TYPES" = "all" ] || [ "$DATA_TYPES" = "auth-only" ]; then
            echo "👤 Verifying user accounts..."
            psql "$TARGET_DB_URL" -c "SELECT 'Users found:' as info, COUNT(*) as count FROM auth.users WHERE id IN ($SQL_USER_LIST);" >> "$RESTORE_DIR/verification/restore_verification.txt" 2>/dev/null || echo "User verification failed" >> "$RESTORE_DIR/verification/restore_verification.txt"
          fi

          # Verify trading data
          if [ "$DATA_TYPES" = "all" ] || [ "$DATA_TYPES" = "trading-only" ]; then
            echo "📊 Verifying trading data..."
            psql "$TARGET_DB_URL" -c "SELECT 'Trades found:' as info, COUNT(*) as count FROM public.trades WHERE user_id IN ($SQL_USER_LIST);" >> "$RESTORE_DIR/verification/restore_verification.txt" 2>/dev/null || echo "Trades verification failed" >> "$RESTORE_DIR/verification/restore_verification.txt"
          fi

          # Verify portfolio data
          if [ "$DATA_TYPES" = "all" ] || [ "$DATA_TYPES" = "portfolio-only" ]; then
            echo "💰 Verifying portfolio data..."
            psql "$TARGET_DB_URL" -c "SELECT 'Portfolio entries found:' as info, COUNT(*) as count FROM public.portfolio_data WHERE user_id IN ($SQL_USER_LIST);" >> "$RESTORE_DIR/verification/restore_verification.txt" 2>/dev/null || echo "Portfolio verification failed" >> "$RESTORE_DIR/verification/restore_verification.txt"
          fi

          # Display verification results
          echo "📋 Verification Results:"
          cat "$RESTORE_DIR/verification/restore_verification.txt"

          echo "✅ Restore verification completed"

      # Generate restore report
      - name: 'Generate Restore Report'
        continue-on-error: true
        run: |
          echo "📄 Generating restore report..."

          cat > "$RESTORE_DIR/RESTORE_REPORT.md" << EOF
          # User-Specific Restore Report

          **Restore Date:** $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          **Restore Type:** ${{ github.event.inputs.restore_type }}
          **Target Database:** ${{ github.event.inputs.target_database }}
          **Data Types:** ${{ github.event.inputs.data_types }}
          **Backup Source:** $BACKUP_DIR
          **User IDs:** ${{ env.USER_IDS }}
          **User Count:** ${{ env.USER_COUNT }}

          ## Restore Process

          1. ✅ Backup located and validated
          2. ✅ User IDs parsed and validated
          3. ✅ Restore workspace created
          4. ✅ User-specific SQL generated
          5. ✅ Restore executed
          6. ✅ Results verified

          ## Files Created

          - \`$RESTORE_DIR/sql/user_restore.sql\` - Generated restore SQL
          - \`$RESTORE_DIR/logs/\` - Execution logs
          - \`$RESTORE_DIR/verification/\` - Verification results

          ## Verification Results

          \`\`\`
          $(cat "$RESTORE_DIR/verification/restore_verification.txt" 2>/dev/null || echo "Verification results not available")
          \`\`\`

          ## Safety Notes

          ⚠️ **This restore operation:**
          - Restored data for ${{ env.USER_COUNT }} specific users
          - Used backup from: $BACKUP_DIR
          - Target database: ${{ github.event.inputs.target_database }}
          - Data types: ${{ github.event.inputs.data_types }}

          ## Next Steps

          1. Verify the restored data meets expectations
          2. Test application functionality for restored users
          3. Monitor for any issues or inconsistencies
          4. Keep this restore report for audit purposes

          ---
          *Generated by Nexus Journal User-Specific Restore System*
          EOF

          echo "✅ Restore report generated: $RESTORE_DIR/RESTORE_REPORT.md"

      # Commit restore documentation
      - name: 'Commit Restore Documentation'
        continue-on-error: true
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: |
            🔄 User-Specific Restore - ${{ env.TIMESTAMP }}

            📋 Restore Details:
            - Type: ${{ github.event.inputs.restore_type }}
            - Users: ${{ env.USER_COUNT }} users
            - Data Types: ${{ github.event.inputs.data_types }}
            - Target: ${{ github.event.inputs.target_database }}
            - Backup Source: ${{ env.BACKUP_DIR }}

            🔗 Workflow: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
          file_pattern: 'restore/**'
          commit_user_name: 'nexus-restore-bot'
          commit_user_email: '<EMAIL>'
          commit_author: 'Nexus Restore Bot <<EMAIL>>'
