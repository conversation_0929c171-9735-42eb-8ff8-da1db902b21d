# 🗄️ Complete Database Backup Setup Guide

This guide explains how to set up **end-to-end automated database backups** for your Nexus Journal trading application, including **OAuth data, authentication tables, and all user data**.

## 📋 Overview

We have created a comprehensive backup system with three workflows:

1. **`backup.yml`** - Simple backup on pull requests only (basic Supabase example)
2. **`database-backup.yml`** - **COMPLETE END-TO-END BACKUP** with OAuth, auth data, and all application data
3. **Full automation** with daily schedule, PR events, manual triggers, and intelligent cleanup

## 🎯 What Gets Backed Up

### 🔐 Authentication & OAuth Data
- ✅ **User accounts** (`auth.users`)
- ✅ **OAuth identities** (`auth.identities`) - Google, GitHub, etc.
- ✅ **Active sessions** (`auth.sessions`)
- ✅ **JWT refresh tokens** (`auth.refresh_tokens`)
- ✅ **Database roles and permissions**
- ✅ **Complete auth schema structure**

### 📊 Application Data
- ✅ **Trading data** (`trades` table)
- ✅ **Portfolio data** (capitals, changes, overrides)
- ✅ **User preferences** and settings
- ✅ **Chart images** and binary data
- ✅ **Tax data** and calculations
- ✅ **Dashboard configurations**
- ✅ **Miscellaneous user data**
- ✅ **Complete database schema**

### 📋 Metadata & Documentation
- ✅ **Database statistics** and user counts
- ✅ **Backup verification** and integrity checks
- ✅ **Complete restoration instructions**
- ✅ **Backup manifests** and file listings

## 🔧 Setup Instructions

### Step 1: Get Your Supabase Database URL

1. Go to your Supabase project dashboard
2. Navigate to **Settings** → **Database**
3. Copy the **Connection string** that looks like:
   ```
   postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-REF].supabase.co:5432/postgres
   ```
4. **IMPORTANT**: This URL must have **full database access** to backup auth schema

### Step 2: Add Required GitHub Secrets

1. Go to your GitHub repository
2. Navigate to **Settings** → **Secrets and variables** → **Actions**
3. Add these secrets:

#### Required Secret:
- **Name**: `	`
- **Value**: Your Supabase connection string from Step 1

#### Optional Secret (for encryption):
- **Name**: `BACKUP_ENCRYPTION_KEY`
- **Value**: A strong encryption key for sensitive backups (optional)

### Step 3: Choose Your Backup Strategy

#### Option A: Simple Backup (backup.yml)
- ✅ Runs only on pull requests
- ✅ Basic schema and data backup
- ✅ Good for testing and validation
- ✅ Follows basic Supabase docs

#### Option B: **COMPLETE END-TO-END BACKUP** (database-backup.yml) ⭐ **RECOMMENDED**
- ✅ **Complete OAuth and authentication data backup**
- ✅ **All user data and trading information**
- ✅ **Daily automated backups** at 2 AM UTC
- ✅ **Backups on code changes** (PR/push to main)
- ✅ **Manual trigger** with backup type options
- ✅ **Intelligent cleanup** (keeps 30 days)
- ✅ **Comprehensive documentation** and restoration guides
- ✅ **Backup verification** and integrity checks
- ✅ **Organized directory structure**

### Step 4: Backup Options (Complete Backup)

When manually triggering the complete backup, you can choose:

#### Backup Types:
- **`complete`** - Everything (auth + data + schema) ⭐ **Recommended**
- **`data-only`** - Only data, no schema
- **`schema-only`** - Only database structure
- **`auth-only`** - Only authentication and OAuth data

#### Binary Data Options:
- **`true`** - Include chart images and binary data ⭐ **Recommended**
- **`false`** - Skip binary data (faster, smaller backups)

## 🚨 CRITICAL Security Warnings

### ⚠️ EXTREMELY SENSITIVE DATA WARNING

**This backup system captures HIGHLY SENSITIVE DATA including:**

🔐 **Authentication & OAuth Data:**
- User passwords (encrypted) and authentication credentials
- OAuth tokens from Google, GitHub, and other providers
- JWT refresh tokens and session data
- User personal information and email addresses

💰 **Financial & Trading Data:**
- Complete trading history and financial performance
- Portfolio values and capital information
- Personal trading strategies and notes
- Tax-related financial data

### 🛡️ MANDATORY Security Requirements

#### Repository Security
1. ✅ Repository MUST be **PRIVATE** - NEVER public
2. ✅ Enable **branch protection** on main branch
3. ✅ Require **2FA** for all repository collaborators
4. ✅ Regular **access audits** - remove unused access
5. ✅ Consider **separate private repository** just for backups

#### Access Control
1. ✅ **Minimum necessary access** - only authorized personnel
2. ✅ **Time-limited access** - regular review and removal
3. ✅ **Audit logs** - monitor who accesses backups
4. ✅ **Strong authentication** - require 2FA for all users

#### Data Protection
1. ✅ **GitHub Secrets** - never store credentials in code
2. ✅ **Encrypted secrets** - use strong encryption keys
3. ✅ **Regular rotation** - rotate database passwords monthly
4. ✅ **Monitor access** - watch for unauthorized attempts
5. ✅ **Compliance** - follow GDPR, SOX, and other regulations

#### Additional Security Measures
1. ✅ **Network security** - restrict database access by IP
2. ✅ **Backup encryption** - consider encrypting backup files
3. ✅ **Retention policies** - automatic cleanup of old backups
4. ✅ **Incident response** - plan for security breaches
5. ✅ **Regular testing** - verify backup and restore procedures

## 📁 Complete Backup Structure

The end-to-end backup creates a comprehensive directory structure:

```
backups/nexus-journal/YYYYMMDD_HHMMSS/
├── auth/                           # 🔐 Authentication & OAuth Data
│   ├── roles.sql                   # Database roles and permissions
│   ├── auth_schema.sql             # Auth schema structure
│   ├── users.sql                   # User accounts and profiles
│   ├── identities.sql              # OAuth provider identities (Google, GitHub, etc.)
│   ├── sessions.sql                # Active user sessions
│   ├── refresh_tokens.sql          # JWT refresh tokens
│   └── auth_tables_list.txt        # List of auth tables
├── public/                         # 📊 Application Data
│   ├── schema.sql                  # Complete database structure
│   ├── trades.sql                  # Core trading data
│   ├── portfolio_data.sql          # Portfolio capitals and changes
│   ├── user_preferences.sql        # User settings and preferences
│   ├── chart_image_blobs.sql       # Chart images and binary data
│   ├── misc_data.sql               # Miscellaneous user data
│   ├── trade_settings.sql          # Trade journal settings
│   ├── tax_data.sql                # Tax calculation data
│   ├── dashboard_config.sql        # Dashboard configurations
│   ├── commentary_data.sql         # User commentary
│   ├── tables_list.txt             # List of public tables
│   └── chart_images_summary.txt    # Chart images statistics
├── metadata/                       # 📋 Backup Information
│   ├── backup_manifest.json        # Complete backup manifest
│   ├── database_version.txt        # PostgreSQL version info
│   ├── database_info.txt           # Database connection info
│   ├── schema_summary.txt          # Schema statistics
│   ├── user_statistics.txt         # User and auth statistics
│   └── trading_statistics.txt      # Trading data statistics
└── README.md                       # 📚 Complete restoration guide
```

## 🔄 Complete Restoration Process

### Prerequisites
```bash
# Install PostgreSQL client
sudo apt-get install postgresql-client

# Install Supabase CLI (optional)
npm install -g supabase
```

### Step-by-Step Restoration

#### 1. Restore Authentication Infrastructure
```bash
# Restore database roles and permissions (run first)
psql -d "your-database-url" -f auth/roles.sql

# Restore auth schema structure
psql -d "your-database-url" -f auth/auth_schema.sql
```

#### 2. Restore Application Schema
```bash
# Restore complete database structure
psql -d "your-database-url" -f public/schema.sql
```

#### 3. Restore Authentication & OAuth Data
```bash
# Restore user accounts
psql -d "your-database-url" -f auth/users.sql

# Restore OAuth provider identities (Google, GitHub, etc.)
psql -d "your-database-url" -f auth/identities.sql

# Restore active sessions (optional - these expire)
psql -d "your-database-url" -f auth/sessions.sql

# Restore JWT refresh tokens (optional - these expire)
psql -d "your-database-url" -f auth/refresh_tokens.sql
```

#### 4. Restore Application Data
```bash
# Restore core trading data
psql -d "your-database-url" -f public/trades.sql

# Restore portfolio data (capitals, changes, overrides)
psql -d "your-database-url" -f public/portfolio_data.sql

# Restore user preferences and settings
psql -d "your-database-url" -f public/user_preferences.sql

# Restore additional application data
psql -d "your-database-url" -f public/misc_data.sql
psql -d "your-database-url" -f public/trade_settings.sql
psql -d "your-database-url" -f public/tax_data.sql
psql -d "your-database-url" -f public/dashboard_config.sql

# Restore chart images (large file - optional)
psql -d "your-database-url" -f public/chart_image_blobs.sql
```

#### 5. Verify Complete Restoration
```bash
# Check user accounts
psql -d "your-database-url" -c "SELECT COUNT(*) as total_users FROM auth.users;"

# Check OAuth identities
psql -d "your-database-url" -c "SELECT provider, COUNT(*) FROM auth.identities GROUP BY provider;"

# Check trading data
psql -d "your-database-url" -c "SELECT COUNT(*) as total_trades FROM public.trades;"

# Check portfolio data
psql -d "your-database-url" -c "SELECT COUNT(*) as portfolio_entries FROM public.portfolio_data;"
```

## 🎯 Backup Triggers

### Automatic Triggers
- **Daily**: Every day at midnight UTC (00:00)
- **Pull Requests**: When PRs are created/updated on main branch
- **Pushes**: When code is pushed to main branch

### Manual Trigger
1. Go to **Actions** tab in your GitHub repository
2. Select **Supa-backup** workflow
3. Click **Run workflow**
4. Choose your backup type and run

## 🧹 Backup Retention

The comprehensive backup automatically:
- ✅ Keeps backups for 30 days
- ✅ Cleans up old backups during scheduled runs
- ✅ Maintains backup history in git

## 🔍 Monitoring Backups

### Check Backup Status
1. Go to **Actions** tab in your repository
2. Look for green ✅ or red ❌ status indicators
3. Click on any workflow run to see details

### Backup Notifications
- ✅ GitHub will email you if backups fail
- ✅ Check the Actions tab regularly
- ✅ Set up additional monitoring if needed

## 🛠️ Troubleshooting

### Common Issues

#### ❌ "SUPABASE_DB_URL secret is not set"
**Solution**: Add the `SUPABASE_DB_URL` secret in repository settings

#### ❌ "Authentication failed"
**Solution**: Check your database URL and password are correct

#### ❌ "Permission denied"
**Solution**: Ensure your database user has backup permissions

#### ❌ "Workflow not running"
**Solution**: Check if Actions are enabled in repository settings

### Getting Help

1. Check the workflow logs in the Actions tab
2. Verify your Supabase connection string
3. Test the connection manually using Supabase CLI
4. Contact support if issues persist

## 📊 Best Practices

### 🔄 Regular Testing
- ✅ Test restore process monthly
- ✅ Verify backup completeness
- ✅ Monitor backup file sizes

### 🔐 Security Practices
- ✅ Use least-privilege database credentials
- ✅ Regularly audit repository access
- ✅ Keep backup repository private
- ✅ Monitor for unauthorized access

### 📈 Monitoring
- ✅ Set up alerts for backup failures
- ✅ Monitor backup storage usage
- ✅ Track backup success rates

## 🎉 You're All Set!

Your Nexus Journal database is now protected with automated backups. The system will:

1. 🔄 Create daily backups automatically
2. 📊 Backup on every code change
3. 🗄️ Store backups securely in your repository
4. 🧹 Clean up old backups automatically
5. 📧 Notify you of any issues

Remember to keep your repository private and test your restore process regularly!
