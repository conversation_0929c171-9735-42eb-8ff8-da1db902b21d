{"name": "nexus-journal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc --noEmit && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@heroui/react": "2.7.8", "@heroui/use-theme": "2.1.6", "@iconify/react": "latest", "@nivo/bar": "^0.99.0", "@nivo/core": "^0.99.0", "@nivo/pie": "^0.99.0", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-virtual": "^3.13.10", "@types/recharts": "^1.8.29", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.5.0", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "papaparse": "^5.5.3", "react": "^18.3.1", "react-calendar-heatmap": "^1.10.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-router-dom": "5.3.4", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "source-map-js": "^1.2.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/generator": "^7.27.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@babel/traverse": "^7.27.0", "@babel/types": "^7.27.0", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.5", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "10.4.20", "postcss": "8.4.49", "tailwindcss": "3.4.17", "terser": "^5.43.1", "typescript": "5.7.3", "vite": "^6.0.11"}}