rolname,query,calls,total_time,prop_total_time,index_advisor_result
authenticated,"WITH pgrst_source AS (INSERT INTO ""public"".""trades""(""allocation"", ""avg_entry"", ""avg_exit_price"", ""base_duration"", ""buy_sell"", ""chart_attachments"", ""cmp"", ""cmp_auto_fetched"", ""cumm_pf"", ""date"", ""entry"", ""exit1_date"", ""exit1_price"", ""exit1_qty"", ""exit2_date"", ""exit2_price"", ""exit2_qty"", ""exit3_date"", ""exit3_price"", ""exit3_qty"", ""exit_trigger"", ""exited_qty"", ""holding_days"", ""id"", ""initial_qty"", ""name"", ""needs_recalculation"", ""notes"", ""open_heat"", ""open_qty"", ""pf_impact"", ""pl_rs"", ""plan_followed"", ""position_size"", ""position_status"", ""proficiency_growth_areas"", ""pyramid1_date"", ""pyramid1_price"", ""pyramid1_qty"", ""pyramid2_date"", ""pyramid2_price"", ""pyramid2_qty"", ""realised_amount"", ""reward_risk"", ""sector"", ""setup"", ""sl"", ""sl_percent"", ""stock_move"", ""trade_no"", ""tsl"", ""user_edited_fields"", ""user_id"") SELECT ""pgrst_body"".""allocation"", ""pgrst_body"".""avg_entry"", ""pgrst_body"".""avg_exit_price"", ""pgrst_body"".""base_duration"", ""pgrst_body"".""buy_sell"", ""pgrst_body"".""chart_attachments"", ""pgrst_body"".""cmp"", ""pgrst_body"".""cmp_auto_fetched"", ""pgrst_body"".""cumm_pf"", ""pgrst_body"".""date"", ""pgrst_body"".""entry"", ""pgrst_body"".""exit1_date"", ""pgrst_body"".""exit1_price"", ""pgrst_body"".""exit1_qty"", ""pgrst_body"".""exit2_date"", ""pgrst_body"".""exit2_price"", ""pgrst_body"".""exit2_qty"", ""pgrst_body"".""exit3_date"", ""pgrst_body"".""exit3_price"", ""pgrst_body"".""exit3_qty"", ""pgrst_body"".""exit_trigger"", ""pgrst_body"".""exited_qty"", ""pgrst_body"".""holding_days"", ""pgrst_body"".""id"", ""pgrst_body"".""initial_qty"", ""pgrst_body"".""name"", ""pgrst_body"".""needs_recalculation"", ""pgrst_body"".""notes"", ""pgrst_body"".""open_heat"", ""pgrst_body"".""open_qty"", ""pgrst_body"".""pf_impact"", ""pgrst_body"".""pl_rs"", ""pgrst_body"".""plan_followed"", ""pgrst_body"".""position_size"", ""pgrst_body"".""position_status"", ""pgrst_body"".""proficiency_growth_areas"", ""pgrst_body"".""pyramid1_date"", ""pgrst_body"".""pyramid1_price"", ""pgrst_body"".""pyramid1_qty"", ""pgrst_body"".""pyramid2_date"", ""pgrst_body"".""pyramid2_price"", ""pgrst_body"".""pyramid2_qty"", ""pgrst_body"".""realised_amount"", ""pgrst_body"".""reward_risk"", ""pgrst_body"".""sector"", ""pgrst_body"".""setup"", ""pgrst_body"".""sl"", ""pgrst_body"".""sl_percent"", ""pgrst_body"".""stock_move"", ""pgrst_body"".""trade_no"", ""pgrst_body"".""tsl"", ""pgrst_body"".""user_edited_fields"", ""pgrst_body"".""user_id"" FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT ""allocation"", ""avg_entry"", ""avg_exit_price"", ""base_duration"", ""buy_sell"", ""chart_attachments"", ""cmp"", ""cmp_auto_fetched"", ""cumm_pf"", ""date"", ""entry"", ""exit1_date"", ""exit1_price"", ""exit1_qty"", ""exit2_date"", ""exit2_price"", ""exit2_qty"", ""exit3_date"", ""exit3_price"", ""exit3_qty"", ""exit_trigger"", ""exited_qty"", ""holding_days"", ""id"", ""initial_qty"", ""name"", ""needs_recalculation"", ""notes"", ""open_heat"", ""open_qty"", ""pf_impact"", ""pl_rs"", ""plan_followed"", ""position_size"", ""position_status"", ""proficiency_growth_areas"", ""pyramid1_date"", ""pyramid1_price"", ""pyramid1_qty"", ""pyramid2_date"", ""pyramid2_price"", ""pyramid2_qty"", ""realised_amount"", ""reward_risk"", ""sector"", ""setup"", ""sl"", ""sl_percent"", ""stock_move"", ""trade_no"", ""tsl"", ""user_edited_fields"", ""user_id"" FROM json_to_recordset(pgrst_payload.json_data) AS _(""allocation"" numeric(8,4), ""avg_entry"" numeric(15,4), ""avg_exit_price"" numeric(15,4), ""base_duration"" text, ""buy_sell"" text, ""chart_attachments"" jsonb, ""cmp"" numeric(15,4), ""cmp_auto_fetched"" boolean, ""cumm_pf"" numeric(8,4), ""date"" date, ""entry"" numeric(15,4), ""exit1_date"" date, ""exit1_price"" numeric(15,4), ""exit1_qty"" integer, ""exit2_date"" date, ""exit2_price"" numeric(15,4), ""exit2_qty"" integer, ""exit3_date"" date, ""exit3_price"" numeric(15,4), ""exit3_qty"" integer, ""exit_trigger"" text, ""exited_qty"" integer, ""holding_days"" integer, ""id"" uuid, ""initial_qty"" integer, ""name"" text, ""needs_recalculation"" boolean, ""notes"" text, ""open_heat"" numeric(8,4), ""open_qty"" integer, ""pf_impact"" numeric(8,4), ""pl_rs"" numeric(15,2), ""plan_followed"" boolean, ""position_size"" numeric(15,2), ""position_status"" text, ""proficiency_growth_areas"" text, ""pyramid1_date"" date, ""pyramid1_price"" numeric(15,4), ""pyramid1_qty"" integer, ""pyramid2_date"" date, ""pyramid2_price"" numeric(15,4), ""pyramid2_qty"" integer, ""realised_amount"" numeric(15,2), ""reward_risk"" numeric(8,4), ""sector"" text, ""setup"" text, ""sl"" numeric(15,4), ""sl_percent"" numeric(8,4), ""stock_move"" numeric(8,4), ""trade_no"" text, ""tsl"" numeric(15,4), ""user_edited_fields"" text[], ""user_id"" uuid) ) pgrst_body  RETURNING $2) SELECT $3 AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, array[]::text[] AS header, $4::text AS body, nullif(current_setting($5, $6), $7) AS response_headers, nullif(current_setting($8, $9), $10) AS response_status, $11 AS response_inserted FROM (SELECT * FROM pgrst_source) _postgrest_t",112310,1841736.66838999,46.0%,"{""has_suggestion"":null,""startup_cost_before"":null,""startup_cost_after"":null,""total_cost_before"":null,""total_cost_after"":null,""index_statements"":[]}"
authenticated,"WITH pgrst_source AS ( SELECT ""public"".""trades"".""id"", ""public"".""trades"".""user_id"", ""public"".""trades"".""trade_no"", ""public"".""trades"".""name"", ""public"".""trades"".""date"", ""public"".""trades"".""entry"", ""public"".""trades"".""avg_entry"", ""public"".""trades"".""sl"", ""public"".""trades"".""tsl"", ""public"".""trades"".""buy_sell"", ""public"".""trades"".""cmp"", ""public"".""trades"".""setup"", ""public"".""trades"".""base_duration"", ""public"".""trades"".""initial_qty"", ""public"".""trades"".""pyramid1_price"", ""public"".""trades"".""pyramid1_qty"", ""public"".""trades"".""pyramid1_date"", ""public"".""trades"".""pyramid2_price"", ""public"".""trades"".""pyramid2_qty"", ""public"".""trades"".""pyramid2_date"", ""public"".""trades"".""position_size"", ""public"".""trades"".""allocation"", ""public"".""trades"".""sl_percent"", ""public"".""trades"".""exit1_price"", ""public"".""trades"".""exit1_qty"", ""public"".""trades"".""exit1_date"", ""public"".""trades"".""exit2_price"", ""public"".""trades"".""exit2_qty"", ""public"".""trades"".""exit2_date"", ""public"".""trades"".""exit3_price"", ""public"".""trades"".""exit3_qty"", ""public"".""trades"".""exit3_date"", ""public"".""trades"".""open_qty"", ""public"".""trades"".""exited_qty"", ""public"".""trades"".""avg_exit_price"", ""public"".""trades"".""stock_move"", ""public"".""trades"".""reward_risk"", ""public"".""trades"".""holding_days"", ""public"".""trades"".""position_status"", ""public"".""trades"".""realised_amount"", ""public"".""trades"".""pl_rs"", ""public"".""trades"".""pf_impact"", ""public"".""trades"".""cumm_pf"", ""public"".""trades"".""plan_followed"", ""public"".""trades"".""exit_trigger"", ""public"".""trades"".""proficiency_growth_areas"", ""public"".""trades"".""sector"", ""public"".""trades"".""open_heat"", ""public"".""trades"".""notes"", ""public"".""trades"".""chart_attachments"", ""public"".""trades"".""user_edited_fields"", ""public"".""trades"".""cmp_auto_fetched"", ""public"".""trades"".""needs_recalculation"", ""public"".""trades"".""created_at"", ""public"".""trades"".""updated_at"" FROM ""public"".""trades""  WHERE  ""public"".""trades"".""user_id"" = $1  ORDER BY ""public"".""trades"".""trade_no"" ASC  LIMIT $2 OFFSET $3 )  SELECT $4::bigint AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, coalesce(json_agg(_postgrest_t), $5) AS body, nullif(current_setting($6, $7), $8) AS response_headers, nullif(current_setting($9, $10), $11) AS response_status, $12 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t",41687,356280.49403,8.9%,"{""has_suggestion"":null,""startup_cost_before"":14.11,""startup_cost_after"":14.11,""total_cost_before"":14.13,""total_cost_after"":14.13,""index_statements"":[]}"
authenticated,"WITH pgrst_source AS ( SELECT ""public"".""trades"".* FROM ""public"".""trades""  WHERE  ""public"".""trades"".""user_id"" = $1   LIMIT $2 OFFSET $3 ) , pgrst_source_count AS (SELECT $5 FROM ""public"".""trades"" WHERE  ""public"".""trades"".""user_id"" = $4) SELECT (SELECT pg_catalog.count(*) FROM pgrst_source_count) AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, $6::text AS body, nullif(current_setting($7, $8), $9) AS response_headers, nullif(current_setting($10, $11), $12) AS response_status, $13 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t",247178,298074.418973998,7.4%,"{""has_suggestion"":null,""startup_cost_before"":39.54,""startup_cost_after"":39.54,""total_cost_before"":39.56,""total_cost_after"":39.56,""index_statements"":[]}"
authenticated,"WITH pgrst_source AS ( SELECT ""public"".""chart_image_blobs"".""data"" FROM ""public"".""chart_image_blobs""  WHERE  ""public"".""chart_image_blobs"".""user_id"" = $1 AND  ""public"".""chart_image_blobs"".""id"" = $2   LIMIT $3 OFFSET $4 )  SELECT $5::bigint AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, coalesce(json_agg(_postgrest_t)->$6, $7) AS body, nullif(current_setting($8, $9), $10) AS response_headers, nullif(current_setting($11, $12), $13) AS response_status, $14 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t",1113,200695.254398,5.0%,"{""has_suggestion"":null,""startup_cost_before"":4.6,""startup_cost_after"":4.6,""total_cost_before"":4.62,""total_cost_after"":4.62,""index_statements"":[]}"
authenticated,"WITH pgrst_source AS (UPDATE ""public"".""trades"" SET ""allocation"" = ""pgrst_body"".""allocation"", ""avg_entry"" = ""pgrst_body"".""avg_entry"", ""avg_exit_price"" = ""pgrst_body"".""avg_exit_price"", ""base_duration"" = ""pgrst_body"".""base_duration"", ""buy_sell"" = ""pgrst_body"".""buy_sell"", ""chart_attachments"" = ""pgrst_body"".""chart_attachments"", ""cmp"" = ""pgrst_body"".""cmp"", ""cmp_auto_fetched"" = ""pgrst_body"".""cmp_auto_fetched"", ""cumm_pf"" = ""pgrst_body"".""cumm_pf"", ""date"" = ""pgrst_body"".""date"", ""entry"" = ""pgrst_body"".""entry"", ""exit1_date"" = ""pgrst_body"".""exit1_date"", ""exit1_price"" = ""pgrst_body"".""exit1_price"", ""exit1_qty"" = ""pgrst_body"".""exit1_qty"", ""exit2_date"" = ""pgrst_body"".""exit2_date"", ""exit2_price"" = ""pgrst_body"".""exit2_price"", ""exit2_qty"" = ""pgrst_body"".""exit2_qty"", ""exit3_date"" = ""pgrst_body"".""exit3_date"", ""exit3_price"" = ""pgrst_body"".""exit3_price"", ""exit3_qty"" = ""pgrst_body"".""exit3_qty"", ""exit_trigger"" = ""pgrst_body"".""exit_trigger"", ""exited_qty"" = ""pgrst_body"".""exited_qty"", ""holding_days"" = ""pgrst_body"".""holding_days"", ""id"" = ""pgrst_body"".""id"", ""initial_qty"" = ""pgrst_body"".""initial_qty"", ""name"" = ""pgrst_body"".""name"", ""needs_recalculation"" = ""pgrst_body"".""needs_recalculation"", ""notes"" = ""pgrst_body"".""notes"", ""open_qty"" = ""pgrst_body"".""open_qty"", ""pf_impact"" = ""pgrst_body"".""pf_impact"", ""pl_rs"" = ""pgrst_body"".""pl_rs"", ""plan_followed"" = ""pgrst_body"".""plan_followed"", ""position_size"" = ""pgrst_body"".""position_size"", ""position_status"" = ""pgrst_body"".""position_status"", ""proficiency_growth_areas"" = ""pgrst_body"".""proficiency_growth_areas"", ""pyramid1_date"" = ""pgrst_body"".""pyramid1_date"", ""pyramid1_price"" = ""pgrst_body"".""pyramid1_price"", ""pyramid1_qty"" = ""pgrst_body"".""pyramid1_qty"", ""pyramid2_date"" = ""pgrst_body"".""pyramid2_date"", ""pyramid2_price"" = ""pgrst_body"".""pyramid2_price"", ""pyramid2_qty"" = ""pgrst_body"".""pyramid2_qty"", ""realised_amount"" = ""pgrst_body"".""realised_amount"", ""reward_risk"" = ""pgrst_body"".""reward_risk"", ""sector"" = ""pgrst_body"".""sector"", ""setup"" = ""pgrst_body"".""setup"", ""sl"" = ""pgrst_body"".""sl"", ""sl_percent"" = ""pgrst_body"".""sl_percent"", ""stock_move"" = ""pgrst_body"".""stock_move"", ""trade_no"" = ""pgrst_body"".""trade_no"", ""tsl"" = ""pgrst_body"".""tsl"", ""user_edited_fields"" = ""pgrst_body"".""user_edited_fields"", ""user_id"" = ""pgrst_body"".""user_id"" FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT ""allocation"", ""avg_entry"", ""avg_exit_price"", ""base_duration"", ""buy_sell"", ""chart_attachments"", ""cmp"", ""cmp_auto_fetched"", ""cumm_pf"", ""date"", ""entry"", ""exit1_date"", ""exit1_price"", ""exit1_qty"", ""exit2_date"", ""exit2_price"", ""exit2_qty"", ""exit3_date"", ""exit3_price"", ""exit3_qty"", ""exit_trigger"", ""exited_qty"", ""holding_days"", ""id"", ""initial_qty"", ""name"", ""needs_recalculation"", ""notes"", ""open_qty"", ""pf_impact"", ""pl_rs"", ""plan_followed"", ""position_size"", ""position_status"", ""proficiency_growth_areas"", ""pyramid1_date"", ""pyramid1_price"", ""pyramid1_qty"", ""pyramid2_date"", ""pyramid2_price"", ""pyramid2_qty"", ""realised_amount"", ""reward_risk"", ""sector"", ""setup"", ""sl"", ""sl_percent"", ""stock_move"", ""trade_no"", ""tsl"", ""user_edited_fields"", ""user_id"" FROM json_to_record(pgrst_payload.json_data) AS _(""allocation"" numeric(10,4), ""avg_entry"" numeric(12,4), ""avg_exit_price"" numeric(12,4), ""base_duration"" text, ""buy_sell"" text, ""chart_attachments"" jsonb, ""cmp"" numeric(12,4), ""cmp_auto_fetched"" boolean, ""cumm_pf"" numeric(10,4), ""date"" date, ""entry"" numeric(12,4), ""exit1_date"" date, ""exit1_price"" numeric(12,4), ""exit1_qty"" numeric(12,4), ""exit2_date"" date, ""exit2_price"" numeric(12,4), ""exit2_qty"" numeric(12,4), ""exit3_date"" date, ""exit3_price"" numeric(12,4), ""exit3_qty"" numeric(12,4), ""exit_trigger"" text, ""exited_qty"" numeric(12,4), ""holding_days"" integer, ""id"" uuid, ""initial_qty"" numeric(12,4), ""name"" text, ""needs_recalculation"" boolean, ""notes"" text, ""open_qty"" numeric(12,4), ""pf_impact"" numeric(10,4), ""pl_rs"" numeric(15,4), ""plan_followed"" boolean, ""position_size"" numeric(15,4), ""position_status"" text, ""proficiency_growth_areas"" text, ""pyramid1_date"" date, ""pyramid1_price"" numeric(12,4), ""pyramid1_qty"" numeric(12,4), ""pyramid2_date"" date, ""pyramid2_price"" numeric(12,4), ""pyramid2_qty"" numeric(12,4), ""realised_amount"" numeric(15,4), ""reward_risk"" numeric(12,4), ""sector"" text, ""setup"" text, ""sl"" numeric(12,4), ""sl_percent"" numeric(10,4), ""stock_move"" numeric(10,4), ""trade_no"" text, ""tsl"" numeric(12,4), ""user_edited_fields"" text[], ""user_id"" uuid) ) pgrst_body  WHERE  ""public"".""trades"".""id"" = $2 AND  ""public"".""trades"".""user_id"" = $3 RETURNING $4) SELECT $5 AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, array[]::text[] AS header, $6::text AS body, nullif(current_setting($7, $8), $9) AS response_headers, nullif(current_setting($10, $11), $12) AS response_status, $13 AS response_inserted FROM (SELECT * FROM pgrst_source) _postgrest_t",27239,110579.782565,2.8%,"{""has_suggestion"":null,""startup_cost_before"":null,""startup_cost_after"":null,""total_cost_before"":null,""total_cost_after"":null,""index_statements"":[]}"
authenticated,"WITH pgrst_source AS (INSERT INTO ""public"".""trade_settings""(""search_query"", ""sort_descriptor"", ""status_filter"", ""user_id"", ""visible_columns"") SELECT ""pgrst_body"".""search_query"", ""pgrst_body"".""sort_descriptor"", ""pgrst_body"".""status_filter"", ""pgrst_body"".""user_id"", ""pgrst_body"".""visible_columns"" FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT ""search_query"", ""sort_descriptor"", ""status_filter"", ""user_id"", ""visible_columns"" FROM json_to_record(pgrst_payload.json_data) AS _(""search_query"" text, ""sort_descriptor"" jsonb, ""status_filter"" text, ""user_id"" uuid, ""visible_columns"" text[]) ) pgrst_body WHERE set_config($2, (coalesce(nullif(current_setting($3, $4), $5)::int, $6) + $7)::text, $8) <> $9 ON CONFLICT(""user_id"") DO UPDATE SET ""search_query"" = EXCLUDED.""search_query"", ""sort_descriptor"" = EXCLUDED.""sort_descriptor"", ""status_filter"" = EXCLUDED.""status_filter"", ""user_id"" = EXCLUDED.""user_id"", ""visible_columns"" = EXCLUDED.""visible_columns""WHERE set_config($10, (coalesce(nullif(current_setting($11, $12), $13)::int, $14) - $15)::text, $16) <> $17 RETURNING $18) SELECT $19 AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, array[]::text[] AS header, $20::text AS body, nullif(current_setting($21, $22), $23) AS response_headers, nullif(current_setting($24, $25), $26) AS response_status, nullif(current_setting($27, $28),$29)::int AS response_inserted FROM (SELECT * FROM pgrst_source) _postgrest_t",85121,94753.8135669999,2.4%,"{""has_suggestion"":null,""startup_cost_before"":null,""startup_cost_after"":null,""total_cost_before"":null,""total_cost_after"":null,""index_statements"":[]}"
authenticated,"select set_config('search_path', $1, true), set_config($2, $3, true), set_config('role', $4, true), set_config('request.jwt.claims', $5, true), set_config('request.method', $6, true), set_config('request.path', $7, true), set_config('request.headers', $8, true), set_config('request.cookies', $9, true)",1729837,85629.0037619939,2.1%,"{""has_suggestion"":null,""startup_cost_before"":0,""startup_cost_after"":0,""total_cost_before"":0.03,""total_cost_after"":0.03,""index_statements"":[]}"
authenticated,"WITH pgrst_source AS (INSERT INTO ""public"".""chart_image_blobs""(""compressed"", ""data"", ""filename"", ""id"", ""image_type"", ""mime_type"", ""original_size"", ""size_bytes"", ""trade_id"", ""uploaded_at"", ""user_id"") SELECT ""pgrst_body"".""compressed"", ""pgrst_body"".""data"", ""pgrst_body"".""filename"", ""pgrst_body"".""id"", ""pgrst_body"".""image_type"", ""pgrst_body"".""mime_type"", ""pgrst_body"".""original_size"", ""pgrst_body"".""size_bytes"", ""pgrst_body"".""trade_id"", ""pgrst_body"".""uploaded_at"", ""pgrst_body"".""user_id"" FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT ""compressed"", ""data"", ""filename"", ""id"", ""image_type"", ""mime_type"", ""original_size"", ""size_bytes"", ""trade_id"", ""uploaded_at"", ""user_id"" FROM json_to_record(pgrst_payload.json_data) AS _(""compressed"" boolean, ""data"" bytea, ""filename"" text, ""id"" uuid, ""image_type"" text, ""mime_type"" text, ""original_size"" integer, ""size_bytes"" integer, ""trade_id"" uuid, ""uploaded_at"" timestamp with time zone, ""user_id"" uuid) ) pgrst_body  RETURNING ""public"".""chart_image_blobs"".*) SELECT $2 AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, array[]::text[] AS header, coalesce(json_agg(_postgrest_t), $3) AS body, nullif(current_setting($4, $5), $6) AS response_headers, nullif(current_setting($7, $8), $9) AS response_status, $10 AS response_inserted FROM (SELECT ""chart_image_blobs"".* FROM ""pgrst_source"" AS ""chart_image_blobs""     ) _postgrest_t",270,70258.723216,1.8%,"{""has_suggestion"":null,""startup_cost_before"":null,""startup_cost_after"":null,""total_cost_before"":null,""total_cost_after"":null,""index_statements"":[]}"
authenticated,"WITH pgrst_source AS (DELETE FROM ""public"".""trades""  WHERE  ""public"".""trades"".""user_id"" = $1 RETURNING $2) SELECT $3 AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, array[]::text[] AS header, $4::text AS body, nullif(current_setting($5, $6), $7) AS response_headers, nullif(current_setting($8, $9), $10) AS response_status, $11 AS response_inserted FROM (SELECT * FROM pgrst_source) _postgrest_t",68871,67508.4605759994,1.7%,"{""has_suggestion"":null,""startup_cost_before"":68.39,""startup_cost_after"":68.39,""total_cost_before"":68.41,""total_cost_after"":68.41,""index_statements"":[]}"
authenticated,"WITH pgrst_source AS (INSERT INTO ""public"".""misc_data""(""key"", ""user_id"", ""value"") SELECT ""pgrst_body"".""key"", ""pgrst_body"".""user_id"", ""pgrst_body"".""value"" FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT ""key"", ""user_id"", ""value"" FROM json_to_record(pgrst_payload.json_data) AS _(""key"" text, ""user_id"" uuid, ""value"" jsonb) ) pgrst_body WHERE set_config($2, (coalesce(nullif(current_setting($3, $4), $5)::int, $6) + $7)::text, $8) <> $9 ON CONFLICT(""user_id"", ""key"") DO UPDATE SET ""key"" = EXCLUDED.""key"", ""user_id"" = EXCLUDED.""user_id"", ""value"" = EXCLUDED.""value""WHERE set_config($10, (coalesce(nullif(current_setting($11, $12), $13)::int, $14) - $15)::text, $16) <> $17 RETURNING $18) SELECT $19 AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, array[]::text[] AS header, $20::text AS body, nullif(current_setting($21, $22), $23) AS response_headers, nullif(current_setting($24, $25), $26) AS response_status, nullif(current_setting($27, $28),$29)::int AS response_inserted FROM (SELECT * FROM pgrst_source) _postgrest_t",40920,54205.6560140003,1.4%,"{""has_suggestion"":null,""startup_cost_before"":null,""startup_cost_after"":null,""total_cost_before"":null,""total_cost_after"":null,""index_statements"":[]}"
authenticated,"WITH pgrst_source AS ( SELECT ""public"".""misc_data"".""value"" FROM ""public"".""misc_data""  WHERE  ""public"".""misc_data"".""user_id"" = $1 AND  ""public"".""misc_data"".""key"" = $2   LIMIT $3 OFFSET $4 )  SELECT $5::bigint AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, coalesce(json_agg(_postgrest_t), $6) AS body, nullif(current_setting($7, $8), $9) AS response_headers, nullif(current_setting($10, $11), $12) AS response_status, $13 AS response_inserted FROM ( SELECT * FROM pgrst_source ) _postgrest_t",478040,49416.5008740011,1.2%,"{""has_suggestion"":null,""startup_cost_before"":4.74,""startup_cost_after"":4.74,""total_cost_before"":4.76,""total_cost_after"":4.76,""index_statements"":[]}"
postgres,"with tables as (SELECT
  c.oid :: int8 AS id,
  nc.nspname AS schema,
  c.relname AS name,
  c.relrowsecurity AS rls_enabled,
  c.relforcerowsecurity AS rls_forced,
  CASE
    WHEN c.relreplident = $1 THEN $2
    WHEN c.relreplident = $3 THEN $4
    WHEN c.relreplident = $5 THEN $6
    ELSE $7
  END AS replica_identity,
  pg_total_relation_size(format($8, nc.nspname, c.relname)) :: int8 AS bytes,
  pg_size_pretty(
    pg_total_relation_size(format($9, nc.nspname, c.relname))
  ) AS size,
  pg_stat_get_live_tuples(c.oid) AS live_rows_estimate,
  pg_stat_get_dead_tuples(c.oid) AS dead_rows_estimate,
  obj_description(c.oid) AS comment,
  coalesce(pk.primary_keys, $10) as primary_keys,
  coalesce(
    jsonb_agg(relationships) filter (where relationships is not null),
    $11
  ) as relationships
FROM
  pg_namespace nc
  JOIN pg_class c ON nc.oid = c.relnamespace
  left join (
    select
      table_id,
      jsonb_agg(_pk.*) as primary_keys
    from (
      select
        n.nspname as schema,
        c.relname as table_name,
        a.attname as name,
        c.oid :: int8 as table_id
      from
        pg_index i,
        pg_class c,
        pg_attribute a,
        pg_namespace n
      where
        i.indrelid = c.oid
        and c.relnamespace = n.oid
        and a.attrelid = c.oid
        and a.attnum = any (i.indkey)
        and i.indisprimary
    ) as _pk
    group by table_id
  ) as pk
  on pk.table_id = c.oid
  left join (
    select
      c.oid :: int8 as id,
      c.conname as constraint_name,
      nsa.nspname as source_schema,
      csa.relname as source_table_name,
      sa.attname as source_column_name,
      nta.nspname as target_table_schema,
      cta.relname as target_table_name,
      ta.attname as target_column_name
    from
      pg_constraint c
    join (
      pg_attribute sa
      join pg_class csa on sa.attrelid = csa.oid
      join pg_namespace nsa on csa.relnamespace = nsa.oid
    ) on sa.attrelid = c.conrelid and sa.attnum = any (c.conkey)
    join (
      pg_attribute ta
      join pg_class cta on ta.attrelid = cta.oid
      join pg_namespace nta on cta.relnamespace = nta.oid
    ) on ta.attrelid = c.confrelid and ta.attnum = any (c.confkey)
    where
      c.contype = $12
  ) as relationships
  on (relationships.source_schema = nc.nspname and relationships.source_table_name = c.relname)
  or (relationships.target_table_schema = nc.nspname and relationships.target_table_name = c.relname)
WHERE
  c.relkind IN ($13, $14)
  AND NOT pg_is_other_temp_schema(nc.oid)
  AND (
    pg_has_role(c.relowner, $15)
    OR has_table_privilege(
      c.oid,
      $16
    )
    OR has_any_column_privilege(c.oid, $17)
  )
group by
  c.oid,
  c.relname,
  c.relrowsecurity,
  c.relforcerowsecurity,
  c.relreplident,
  nc.nspname,
  pk.primary_keys
)
  , columns as (-- Adapted from information_schema.columns

SELECT
  c.oid :: int8 AS table_id,
  nc.nspname AS schema,
  c.relname AS table,
  (c.oid || $18 || a.attnum) AS id,
  a.attnum AS ordinal_position,
  a.attname AS name,
  CASE
    WHEN a.atthasdef THEN pg_get_expr(ad.adbin, ad.adrelid)
    ELSE $19
  END AS default_value,
  CASE
    WHEN t.typtype = $20 THEN CASE
      WHEN bt.typelem <> $21 :: oid
      AND bt.typlen = $22 THEN $23
      WHEN nbt.nspname = $24 THEN format_type(t.typbasetype, $25)
      ELSE $26
    END
    ELSE CASE
      WHEN t.typelem <> $27 :: oid
      AND t.typlen = $28 THEN $29
      WHEN nt.nspname = $30 THEN format_type(a.atttypid, $31)
      ELSE $32
    END
  END AS data_type,
  COALESCE(bt.typname, t.typname) AS format,
  a.attidentity IN ($33, $34) AS is_identity,
  CASE
    a.attidentity
    WHEN $35 THEN $36
    WHEN $37 THEN $38
    ELSE $39
  END AS identity_generation,
  a.attgenerated IN ($40) AS is_generated,
  NOT (
    a.attnotnull
    OR t.typtype = $41 AND t.typnotnull
  ) AS is_nullable,
  (
    c.relkind IN ($42, $43)
    OR c.relkind IN ($44, $45) AND pg_column_is_updatable(c.oid, a.attnum, $46)
  ) AS is_updatable,
  uniques.table_id IS NOT NULL AS is_unique,
  check_constraints.definition AS ""check"",
  array_to_json(
    array(
      SELECT
        enumlabel
      FROM
        pg_catalog.pg_enum enums
      WHERE
        enums.enumtypid = coalesce(bt.oid, t.oid)
        OR enums.enumtypid = coalesce(bt.typelem, t.typelem)
      ORDER BY
        enums.enumsortorder
    )
  ) AS enums,
  col_description(c.oid, a.attnum) AS comment
FROM
  pg_attribute a
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid
  AND a.attnum = ad.adnum
  JOIN (
    pg_class c
    JOIN pg_namespace nc ON c.relnamespace = nc.oid
  ) ON a.attrelid = c.oid
  JOIN (
    pg_type t
    JOIN pg_namespace nt ON t.typnamespace = nt.oid
  ) ON a.atttypid = t.oid
  LEFT JOIN (
    pg_type bt
    JOIN pg_namespace nbt ON bt.typnamespace = nbt.oid
  ) ON t.typtype = $47
  AND t.typbasetype = bt.oid
  LEFT JOIN (
    SELECT DISTINCT ON (table_id, ordinal_position)
      conrelid AS table_id,
      conkey[$48] AS ordinal_position
    FROM pg_catalog.pg_constraint
    WHERE contype = $49 AND cardinality(conkey) = $50
  ) AS uniques ON uniques.table_id = c.oid AND uniques.ordinal_position = a.attnum
  LEFT JOIN (
    -- We only select the first column check
    SELECT DISTINCT ON (table_id, ordinal_position)
      conrelid AS table_id,
      conkey[$51] AS ordinal_position,
      substring(
        pg_get_constraintdef(pg_constraint.oid, $52),
        $53,
        length(pg_get_constraintdef(pg_constraint.oid, $54)) - $55
      ) AS ""definition""
    FROM pg_constraint
    WHERE contype = $56 AND cardinality(conkey) = $57
    ORDER BY table_id, ordinal_position, oid asc
  ) AS check_constraints ON check_constraints.table_id = c.oid AND check_constraints.ordinal_position = a.attnum
WHERE
  NOT pg_is_other_temp_schema(nc.oid)
  AND a.attnum > $58
  AND NOT a.attisdropped
  AND (c.relkind IN ($59, $60, $61, $62, $63))
  AND (
    pg_has_role(c.relowner, $64)
    OR has_column_privilege(
      c.oid,
      a.attnum,
      $65
    )
  )
)
select
  *
  , 
COALESCE(
  (
    SELECT
      array_agg(row_to_json(columns)) FILTER (WHERE columns.table_id = tables.id)
    FROM
      columns
  ),
  $66
) AS columns
from tables where schema IN ($67)",253,35386.813718,0.9%,null
authenticated,"WITH pgrst_source AS (INSERT INTO ""public"".""user_preferences""(""accounting_method"", ""created_at"", ""id"", ""is_full_width_enabled"", ""is_mobile_menu_open"", ""is_profile_open"", ""theme"", ""updated_at"", ""user_id"", ""user_name"") SELECT ""pgrst_body"".""accounting_method"", ""pgrst_body"".""created_at"", ""pgrst_body"".""id"", ""pgrst_body"".""is_full_width_enabled"", ""pgrst_body"".""is_mobile_menu_open"", ""pgrst_body"".""is_profile_open"", ""pgrst_body"".""theme"", ""pgrst_body"".""updated_at"", ""pgrst_body"".""user_id"", ""pgrst_body"".""user_name"" FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT ""accounting_method"", ""created_at"", ""id"", ""is_full_width_enabled"", ""is_mobile_menu_open"", ""is_profile_open"", ""theme"", ""updated_at"", ""user_id"", ""user_name"" FROM json_to_record(pgrst_payload.json_data) AS _(""accounting_method"" text, ""created_at"" timestamp with time zone, ""id"" uuid, ""is_full_width_enabled"" boolean, ""is_mobile_menu_open"" boolean, ""is_profile_open"" boolean, ""theme"" text, ""updated_at"" timestamp with time zone, ""user_id"" uuid, ""user_name"" text) ) pgrst_body WHERE set_config($2, (coalesce(nullif(current_setting($3, $4), $5)::int, $6) + $7)::text, $8) <> $9 ON CONFLICT(""user_id"") DO UPDATE SET ""accounting_method"" = EXCLUDED.""accounting_method"", ""created_at"" = EXCLUDED.""created_at"", ""id"" = EXCLUDED.""id"", ""is_full_width_enabled"" = EXCLUDED.""is_full_width_enabled"", ""is_mobile_menu_open"" = EXCLUDED.""is_mobile_menu_open"", ""is_profile_open"" = EXCLUDED.""is_profile_open"", ""theme"" = EXCLUDED.""theme"", ""updated_at"" = EXCLUDED.""updated_at"", ""user_id"" = EXCLUDED.""user_id"", ""user_name"" = EXCLUDED.""user_name""WHERE set_config($10, (coalesce(nullif(current_setting($11, $12), $13)::int, $14) - $15)::text, $16) <> $17 RETURNING $18) SELECT $19 AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, array[]::text[] AS header, $20::text AS body, nullif(current_setting($21, $22), $23) AS response_headers, nullif(current_setting($24, $25), $26) AS response_status, nullif(current_setting($27, $28),$29)::int AS response_inserted FROM (SELECT * FROM pgrst_source) _postgrest_t",47407,34840.**********,0.9%,"{""has_suggestion"":null,""startup_cost_before"":null,""startup_cost_after"":null,""total_cost_before"":null,""total_cost_after"":null,""index_statements"":[]}"
authenticated,"WITH pgrst_source AS (INSERT INTO ""public"".""milestones_data""(""achievements"", ""user_id"") SELECT ""pgrst_body"".""achievements"", ""pgrst_body"".""user_id"" FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT ""achievements"", ""user_id"" FROM json_to_record(pgrst_payload.json_data) AS _(""achievements"" jsonb, ""user_id"" uuid) ) pgrst_body WHERE set_config($2, (coalesce(nullif(current_setting($3, $4), $5)::int, $6) + $7)::text, $8) <> $9 ON CONFLICT(""user_id"") DO UPDATE SET ""achievements"" = EXCLUDED.""achievements"", ""user_id"" = EXCLUDED.""user_id""WHERE set_config($10, (coalesce(nullif(current_setting($11, $12), $13)::int, $14) - $15)::text, $16) <> $17 RETURNING $18) SELECT $19 AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, array[]::text[] AS header, $20::text AS body, nullif(current_setting($21, $22), $23) AS response_headers, nullif(current_setting($24, $25), $26) AS response_status, nullif(current_setting($27, $28),$29)::int AS response_inserted FROM (SELECT * FROM pgrst_source) _postgrest_t",16991,32397.874384,0.8%,"{""has_suggestion"":null,""startup_cost_before"":null,""startup_cost_after"":null,""total_cost_before"":null,""total_cost_after"":null,""index_statements"":[]}"
supabase_auth_admin,"SELECT users.aud, users.banned_until, users.confirmation_sent_at, users.confirmation_token, users.confirmed_at, users.created_at, users.deleted_at, users.email, users.email_change, users.email_change_confirm_status, users.email_change_sent_at, users.email_change_token_current, users.email_change_token_new, users.email_confirmed_at, users.encrypted_password, users.id, users.instance_id, users.invited_at, users.is_anonymous, users.is_sso_user, users.last_sign_in_at, users.phone, users.phone_change, users.phone_change_sent_at, users.phone_change_token, users.phone_confirmed_at, users.raw_app_meta_data, users.raw_user_meta_data, users.reauthentication_sent_at, users.reauthentication_token, users.recovery_sent_at, users.recovery_token, users.role, users.updated_at FROM users AS users WHERE instance_id = $1 and id = $2 LIMIT $3",280664,24265.717168,0.6%,"{""has_suggestion"":null,""startup_cost_before"":null,""startup_cost_after"":null,""total_cost_before"":null,""total_cost_after"":null,""index_statements"":[]}"
supabase_auth_admin,"SELECT identities.created_at, identities.email, identities.id, identities.identity_data, identities.last_sign_in_at, identities.provider, identities.provider_id, identities.updated_at, identities.user_id FROM identities AS identities WHERE user_id = $1",281804,21560.**********,0.5%,"{""has_suggestion"":null,""startup_cost_before"":null,""startup_cost_after"":null,""total_cost_before"":null,""total_cost_after"":null,""index_statements"":[]}"
authenticated,"WITH pgrst_source AS (INSERT INTO ""public"".""portfolio_data""(""amount"", ""type"", ""updated_at"", ""user_id"", ""year"") SELECT ""pgrst_body"".""amount"", ""pgrst_body"".""type"", ""pgrst_body"".""updated_at"", ""pgrst_body"".""user_id"", ""pgrst_body"".""year"" FROM (SELECT $1 AS json_data) pgrst_payload, LATERAL (SELECT ""amount"", ""type"", ""updated_at"", ""user_id"", ""year"" FROM json_to_recordset(pgrst_payload.json_data) AS _(""amount"" numeric(15,2), ""type"" text, ""updated_at"" timestamp with time zone, ""user_id"" uuid, ""year"" integer) ) pgrst_body  RETURNING $2) SELECT $3 AS total_result_set, pg_catalog.count(_postgrest_t) AS page_total, array[]::text[] AS header, $4::text AS body, nullif(current_setting($5, $6), $7) AS response_headers, nullif(current_setting($8, $9), $10) AS response_status, $11 AS response_inserted FROM (SELECT * FROM pgrst_source) _postgrest_t",8413,20327.4792209999,0.5%,"{""has_suggestion"":null,""startup_cost_before"":null,""startup_cost_after"":null,""total_cost_before"":null,""total_cost_after"":null,""index_statements"":[]}"
postgres,"with _base_query as (select * from public.chart_image_blobs order by chart_image_blobs.id asc nulls last limit $1 offset $2)
  select id,user_id,trade_id,case
        when octet_length(image_type::text) > $3 
        then left(image_type::text, $4) || $5
        else image_type::text
      end as image_type,case
        when octet_length(filename::text) > $6 
        then left(filename::text, $7) || $8
        else filename::text
      end as filename,case
        when octet_length(mime_type::text) > $9 
        then left(mime_type::text, $10) || $11
        else mime_type::text
      end as mime_type,size_bytes,case
        when octet_length(data::text) > $12 
        then left(data::text, $13) || $14
        else data::text
      end as data,uploaded_at,compressed,original_size,created_at,updated_at from _base_query",72,18740.929591,0.5%,null
supabase_auth_admin,"SELECT sessions.aal, sessions.created_at, sessions.factor_id, sessions.id, sessions.ip, sessions.not_after, sessions.refreshed_at, sessions.tag, sessions.updated_at, sessions.user_agent, sessions.user_id FROM sessions AS sessions WHERE id = $1 LIMIT $2",283634,18375.550129,0.5%,"{""has_suggestion"":null,""startup_cost_before"":null,""startup_cost_after"":null,""total_cost_before"":null,""total_cost_after"":null,""index_statements"":[]}"
supabase_auth_admin,"INSERT INTO ""refresh_tokens"" (""created_at"", ""instance_id"", ""parent"", ""revoked"", ""session_id"", ""token"", ""updated_at"", ""user_id"") VALUES ($1, $2, $3, $4, $5, $6, $7, $8) returning id",4213,18350.851319,0.5%,null