// Test script for Upstox charges parser
// Run this in the browser console to test the parser directly

async function testUpstoxChargesParser() {
  console.log('🧪 Testing Upstox Charges Parser...');
  
  try {
    // Create a file input element to select the file
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls';
    
    input.onchange = async (event) => {
      const file = event.target.files[0];
      if (!file) {
        console.log('❌ No file selected');
        return;
      }
      
      console.log('📁 Selected file:', file.name);
      
      // Import the parser functions (assuming they're available globally)
      const { isUpstoxPnLStatement, parseUpstoxCharges, formatUpstoxChargesBreakdown } = window;
      
      if (!isUpstoxPnLStatement || !parseUpstoxCharges) {
        console.error('❌ Upstox parser functions not available. Make sure the page is loaded properly.');
        return;
      }
      
      try {
        // Test detection
        console.log('🔍 Testing file detection...');
        const isPnLFile = await isUpstoxPnLStatement(file);
        console.log('✅ Detection result:', isPnLFile);
        
        if (isPnLFile) {
          // Test parsing
          console.log('📊 Testing charges parsing...');
          const charges = await parseUpstoxCharges(file);
          console.log('✅ Parsed charges:', charges);
          
          if (charges && formatUpstoxChargesBreakdown) {
            // Test formatting
            console.log('📋 Testing charges formatting...');
            const formatted = formatUpstoxChargesBreakdown(charges);
            console.log('✅ Formatted charges:', formatted);
          }
        } else {
          console.log('ℹ️ File is not detected as Upstox P&L statement');
        }
        
      } catch (error) {
        console.error('❌ Error during parsing:', error);
      }
    };
    
    // Trigger file selection
    input.click();
    
  } catch (error) {
    console.error('❌ Error setting up test:', error);
  }
}

// Instructions for use
console.log(`
🧪 UPSTOX CHARGES PARSER TEST

To test the Upstox charges parser:

1. Make sure you're on the page with the upload modal
2. Run this command in the console:
   testUpstoxChargesParser()

3. Select your realizedPnL_1920_553498.xlsx file
4. Check the console for results

This will test:
- File detection (isUpstoxPnLStatement)
- Charges parsing (parseUpstoxCharges) 
- Formatting (formatUpstoxChargesBreakdown)
`);

// Make the function available globally
window.testUpstoxChargesParser = testUpstoxChargesParser;
