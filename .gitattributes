# Git LFS configuration for Nexus Journal backup files

# Large backup files should use Git LFS
backups/**/*.sql filter=lfs diff=lfs merge=lfs -text
backups/**/*.json filter=lfs diff=lfs merge=lfs -text
backups/**/*.txt filter=lfs diff=lfs merge=lfs -text

# Specifically handle large data files
*.sql filter=lfs diff=lfs merge=lfs -text
backups/nexus-journal/**/* filter=lfs diff=lfs merge=lfs -text

# Chart images and binary data
backups/**/chart_image_blobs.sql filter=lfs diff=lfs merge=lfs -text
backups/**/trades.sql filter=lfs diff=lfs merge=lfs -text
backups/**/portfolio_data.sql filter=lfs diff=lfs merge=lfs -text
