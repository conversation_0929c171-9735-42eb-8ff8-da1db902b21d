// Test script for Upstox file detection
// Run this in the browser console to test the detection directly

function testUpstoxDetection(filename) {
  console.log('🧪 Testing Upstox detection for:', filename);
  
  // Convert filename to lowercase for case-insensitive comparison
  const lowerFileName = filename.toLowerCase().trim();
  
  // Upstox file naming patterns
  const upstoxPatterns = [
    /trade_\d+_\d+_eq\.xlsx?$/i,     // trade_553498_1920_eq.xlsx
    /upstox.*trade.*\.xlsx?$/i,       // upstox_trade_report.xlsx
    /trade.*upstox.*\.xlsx?$/i,       // trade_report_upstox.xlsx
    /upstox.*\.xlsx?$/i,              // upstox.xlsx
    /realized[Pp]n[Ll].*\.xlsx?$/i,   // realizedPnL_1920_553498.xlsx
    /.*_\d+_\d+.*\.xlsx?$/i           // Any file with _numbers_numbers pattern
  ];
  
  // Check each pattern
  console.log('🔍 Testing patterns against:', lowerFileName);
  upstoxPatterns.forEach((pattern, index) => {
    const matches = pattern.test(lowerFileName);
    console.log(`Pattern ${index + 1}: ${pattern} -> ${matches}`);
  });
  
  // Check if filename matches any Upstox pattern
  const matchesPattern = upstoxPatterns.some(pattern => pattern.test(lowerFileName));
  
  // Check if it's an Excel file
  const isExcelFile = lowerFileName.endsWith('.xlsx') || lowerFileName.endsWith('.xls');
  
  // Final result
  const isUpstoxFormat = matchesPattern && isExcelFile;
  
  console.log('🔍 DETECTION RESULT:', {
    filename,
    lowerFileName,
    matchesPattern,
    isExcelFile,
    isUpstoxFormat
  });
  
  return isUpstoxFormat;
}

// Test with your specific filename
console.log('✅ Result:', testUpstoxDetection('realizedPnL_1920_553498.xlsx'));

// Instructions
console.log(`
🧪 UPSTOX DETECTION TEST

To test with your own filename:
testUpstoxDetection('your-filename.xlsx')

Example:
testUpstoxDetection('realizedPnL_1920_553498.xlsx')
testUpstoxDetection('trade_553498_1920_eq.xlsx')
`);

// Make the function available globally
window.testUpstoxDetection = testUpstoxDetection;
