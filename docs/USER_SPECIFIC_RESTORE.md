# 🔄 User-Specific Data Restore System

This guide explains how to restore data for specific user IDs or perform bulk restores for multiple users, rather than restoring the entire database.

## 🎯 Overview

The User-Specific Restore System allows you to:
- **Single User Restore** - Restore all data for one specific user ID
- **Bulk User Restore** - Restore data for multiple user IDs at once
- **Selective Data Restore** - Choose what types of data to restore per user
- **Granular Control** - Target specific databases (development, staging, production)

## 🚀 How to Use

### **Step 1: Access the Restore Workflow**
1. Go to your repository **Actions** tab
2. Select **"User-Specific Data Restore"** workflow
3. Click **"Run workflow"**

### **Step 2: Configure Restore Options**

#### **Restore Type**
- **`single-user`** - Restore data for exactly one user ID
- **`bulk-users`** - Restore data for multiple user IDs
- **`user-data-only`** - Restore only user data (no auth changes)
- **`user-auth-only`** - Restore only authentication data

#### **User IDs**
- **Single user**: `550e8400-e29b-41d4-a716-************`
- **Multiple users**: `user1,user2,user3` (comma-separated)
- **Format**: UUID format for Supabase user IDs

#### **Backup Date**
- **`latest`** - Use the most recent backup (recommended)
- **Specific date**: `20241206_143022` (YYYYMMDD_HHMMSS format)

#### **Data Types**
- **`all`** - Restore everything for the user(s) ⭐ **Recommended**
- **`trading-only`** - Only trading data (trades, charts)
- **`auth-only`** - Only authentication data (users, OAuth)
- **`portfolio-only`** - Only portfolio data (capitals, changes)
- **`preferences-only`** - Only user settings and preferences

#### **Target Database**
- **`development`** - Safe for testing ⭐ **Recommended**
- **`staging`** - Pre-production environment
- **`production`** - Live database ⚠️ **Use with extreme caution!**

## 📊 Restore Examples

### **Example 1: Single User Complete Restore**
```
Restore Type: single-user
User IDs: 550e8400-e29b-41d4-a716-************
Backup Date: latest
Data Types: all
Target Database: development
```

### **Example 2: Bulk User Trading Data Only**
```
Restore Type: bulk-users
User IDs: user1,user2,user3
Backup Date: 20241206_143022
Data Types: trading-only
Target Database: staging
```

### **Example 3: User Authentication Recovery**
```
Restore Type: single-user
User IDs: 550e8400-e29b-41d4-a716-************
Backup Date: latest
Data Types: auth-only
Target Database: production
```

## 🔍 What Gets Restored

### **Authentication Data** (`auth-only` or `all`)
- ✅ **User accounts** (`auth.users`)
- ✅ **OAuth identities** (`auth.identities`)
- ✅ **Active sessions** (`auth.sessions`)
- ✅ **Refresh tokens** (`auth.refresh_tokens`)

### **Trading Data** (`trading-only` or `all`)
- ✅ **Trades** (`public.trades`)
- ✅ **Chart images** (`public.chart_image_blobs`)
- ✅ **Trade settings** (`public.trade_settings`)

### **Portfolio Data** (`portfolio-only` or `all`)
- ✅ **Portfolio capitals** (`public.portfolio_data`)
- ✅ **Capital changes** and overrides
- ✅ **Performance calculations**

### **User Preferences** (`preferences-only` or `all`)
- ✅ **User settings** (`public.user_preferences`)
- ✅ **Dashboard configurations** (`public.dashboard_config`)
- ✅ **Miscellaneous data** (`public.misc_data`)

## 🛡️ Safety Features

### **Production Protection**
- ⚠️ **Warning messages** for production restores
- ⚠️ **Confirmation delays** before execution
- ⚠️ **Detailed logging** of all operations

### **Data Validation**
- ✅ **User ID format** validation
- ✅ **Backup existence** verification
- ✅ **Target database** connection testing
- ✅ **Restore result** verification

### **Error Handling**
- ✅ **Continue on errors** - Partial restores are better than no restore
- ✅ **Detailed logging** - All operations logged for debugging
- ✅ **Graceful failures** - Clear error messages and fallbacks

## 📁 Generated Files

After a restore, you'll find:

```
restore/user-restore-YYYYMMDD_HHMMSS/
├── sql/
│   ├── user_restore.sql          # Generated restore SQL
│   └── *.sql                     # Backup data files
├── logs/
│   ├── auth_restore.log          # Authentication restore log
│   ├── trades_restore.log        # Trading data restore log
│   └── *.log                     # Other operation logs
├── verification/
│   └── restore_verification.txt  # Verification results
└── RESTORE_REPORT.md             # Complete restore report
```

## 🔧 Advanced Usage

### **Finding User IDs**
```sql
-- Find user IDs by email
SELECT id, email FROM auth.users WHERE email = '<EMAIL>';

-- Find user IDs with recent activity
SELECT DISTINCT user_id FROM public.trades 
WHERE created_at > NOW() - INTERVAL '30 days';
```

### **Checking Available Backups**
```bash
# List available backups
find backups/nexus-journal -type d -name "20*" | sort -r | head -10
```

### **Verifying Restore Results**
```sql
-- Check restored user data
SELECT 'Users' as table_name, COUNT(*) as count 
FROM auth.users WHERE id IN ('user1','user2');

-- Check restored trading data
SELECT 'Trades' as table_name, COUNT(*) as count 
FROM public.trades WHERE user_id IN ('user1','user2');
```

## ⚠️ Important Warnings

### **Production Restores**
- 🚨 **ALWAYS backup current data** before restoring to production
- 🚨 **Verify user IDs** are correct - wrong IDs can overwrite wrong users
- 🚨 **Test on development** first to ensure restore works as expected
- 🚨 **Coordinate with team** - production restores affect live users

### **Data Overwriting**
- ⚠️ **Existing data is overwritten** for specified users
- ⚠️ **No undo functionality** - the restore is permanent
- ⚠️ **Partial restores** may leave data in inconsistent state

### **User ID Validation**
- ⚠️ **UUID format required** - Invalid formats will cause failures
- ⚠️ **Case sensitive** - User IDs must match exactly
- ⚠️ **No spaces** - Trim whitespace from user ID lists

## 🎯 Best Practices

### **Before Restoring**
1. ✅ **Backup current data** for the target users
2. ✅ **Verify user IDs** are correct
3. ✅ **Test on development** environment first
4. ✅ **Coordinate with team** for production restores

### **During Restore**
1. ✅ **Monitor workflow logs** for errors
2. ✅ **Check verification results** after completion
3. ✅ **Test application functionality** for restored users

### **After Restore**
1. ✅ **Verify data integrity** with the restored users
2. ✅ **Test user login** and basic functionality
3. ✅ **Monitor for issues** in the following hours/days
4. ✅ **Keep restore documentation** for audit purposes

## 🆘 Troubleshooting

### **Common Issues**

#### **"User ID not found"**
- Check user ID format (must be valid UUID)
- Verify user exists in the backup
- Check for typos or extra spaces

#### **"Backup not found"**
- Verify backup date format (YYYYMMDD_HHMMSS)
- Check if backup exists in repository
- Use "latest" to find most recent backup

#### **"Database connection failed"**
- Verify `SUPABASE_DB_URL` secret is set
- Check database is running and accessible
- Verify network connectivity

#### **"Partial restore completed"**
- Check workflow logs for specific failures
- Some data types may not exist in backup
- Verify backup contains expected data

### **Getting Help**
1. Check workflow logs in GitHub Actions
2. Review generated restore report
3. Verify backup contents and structure
4. Test database connectivity manually

---

**Your user-specific restore system is ready! You can now restore data for individual users or groups of users without affecting the entire database.** 🎉
