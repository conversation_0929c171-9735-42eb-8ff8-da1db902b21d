import XLSX from 'xlsx';

try {
  console.log('📊 READING UPSTOX CHARGES EXCEL FILE...');
  
  // Read the Excel file
  const workbook = XLSX.readFile('realizedPnL_1920_553498.xlsx');
  
  // Get all sheet names
  console.log('📋 Sheet names:', workbook.SheetNames);
  
  // Process each sheet
  workbook.SheetNames.forEach((sheetName, index) => {
    console.log(`\n📄 SHEET ${index + 1}: "${sheetName}"`);
    console.log('='.repeat(50));
    
    const worksheet = workbook.Sheets[sheetName];
    
    // Convert to JSON with headers
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    console.log(`📊 Total rows: ${jsonData.length}`);
    
    if (jsonData.length > 0) {
      console.log('\n📝 FIRST 30 ROWS:');
      for (let i = 0; i < Math.min(30, jsonData.length); i++) {
        const row = jsonData[i];
        if (row && row.length > 0) {
          console.log(`Row ${i + 1}: ${JSON.stringify(row)}`);
        }
      }
      
      // Look for charges-related data
      console.log('\n🔍 SEARCHING FOR CHARGES DATA:');
      jsonData.forEach((row, index) => {
        if (row && Array.isArray(row)) {
          const rowStr = row.join('|').toLowerCase();
          if (rowStr.includes('charge') || rowStr.includes('tax') || rowStr.includes('fee') || 
              rowStr.includes('brokerage') || rowStr.includes('gst') || rowStr.includes('stt') ||
              rowStr.includes('total') || rowStr.includes('amount') || rowStr.includes('summary')) {
            console.log(`Row ${index + 1} (charges): ${JSON.stringify(row)}`);
          }
        }
      });
      
      // Look for column headers
      console.log('\n📋 POTENTIAL HEADERS:');
      for (let i = 0; i < Math.min(10, jsonData.length); i++) {
        const row = jsonData[i];
        if (row && row.length > 5) {
          console.log(`Row ${i + 1} (potential headers): ${JSON.stringify(row)}`);
        }
      }
    }
  });
  
} catch (error) {
  console.error('❌ Error reading Excel file:', error.message);
}
