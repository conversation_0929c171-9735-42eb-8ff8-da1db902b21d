// Test the date function
const getCurrentISTDate = () => {
  // Get current UTC time
  const now = new Date();
  
  // Create a new date in IST timezone (UTC+5:30)
  // This properly converts UTC to IST without double-adding timezone offset
  const istDate = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Kolkata"}));
  
  return istDate;
};

console.log("Current system date:", new Date());
console.log("IST date:", getCurrentISTDate());
console.log("IST date string:", getCurrentISTDate().toISOString());

// Test the date formatting
const testDate = getCurrentISTDate();
const pad = (num) => num.toString().padStart(2, '0');
const formatted = `${testDate.getFullYear()}-${pad(testDate.getMonth() + 1)}-${pad(testDate.getDate())}`;
console.log("Formatted date:", formatted);
